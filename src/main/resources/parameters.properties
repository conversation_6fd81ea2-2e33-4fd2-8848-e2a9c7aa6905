system.version = v1.0.0

#local.username = sa_staffuser01dev
#local.username=s1104917
local.username = sa_staffuser01dev
#local.username = sa_staffuser02dev
#local.username = sa_staffuser03dev
#local.username = sa_student01dev
#.username=hro-test-078
local.ip=*************

system.name=Visa Management System
system.shortName = VMS
system.copyrightYear = 2025
system.eduhk-name = 2025
system.eduhk-short-name = 2025
system.logout-link = /VMS/logout
system.from-email-address = <EMAIL>

database.schema = 
database.table.prefix = 

system.database.datetimeFormat = DD/MM/YYYY HH24:MI:SS
system.database.dateFormat = DD/MM/YYYY
system.java.dateFormat = dd/MM/yyyy
system.java.datetimeFormat = dd/MM/yyyy HH:mm:ss
#system.js.dateFormat = dd/mm/yyyy
system.js.dateFormat = DD/MM/YYYY
system.js.datetimeFormat = DD/MM/YYYY HH:mm:ss 


system.report.currencyFormat = $#,##0.00_);-$#,##0.00
spring.cloud.gcp.credentials.location=classpath:visionKey.json
spring.cloud.gcp.project-id=second-casing-450501-c0


url: /*[[@{/vision/extract-text(${_csrf.parameterName}=${_csrf.token})}]]*/ "",