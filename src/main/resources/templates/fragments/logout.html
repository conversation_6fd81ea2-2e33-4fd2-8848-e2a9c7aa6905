<div th:fragment="logout" class="logout-container">
    <div class="text-center">
        <a href="#" id="studentLogoutLink" class="logout-link">
            Logout <i class="fas fa-sign-out-alt"></i>
        </a>
        <form th:action="@{/logout}" method="post" id="logoutForm" style="display: none;"></form>
    </div>

    <script>
        $(document).ready(function () {
            $('#studentLogoutLink').on('click', function (e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Sign out',
                    text: 'Are you sure you want to sign out?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#dc3545',
                    cancelButtonColor: '#343a40',
                    confirmButtonText: 'Yes',
                    cancelButtonText: 'No'
                }).then((result) => {
                    if (result.isConfirmed) {
                        let $form = $('#logoutForm');
                        if ($form.length) {
                            $form.submit();
                        } else {
                            console.error("Logout form not found in DOM!");
                        }
                    } else if (result.dismiss === 'cancel') {
                        console.log("User cancelled logout.");
                    }
                });
            });
        });
    </script>
</div>