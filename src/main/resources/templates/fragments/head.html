<head th:fragment="head" xmlns:th="http://www.thymeleaf.org">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>[[${systemName}]]</title>

    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/16x16.png}" sizes="16x16" />
    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/32x32.png}" sizes="32x32" />
    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/96x96.png}" sizes="96x96" />

    <!-- Global stylesheets -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/icons/fontawesome/styles.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/icons/icomoon/styles.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/bootstrap.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/layout.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/components.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/colors.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/myStyles.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/visaPage.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/daterangepicker.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/info-card.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/visaupload.css}" rel="stylesheet" type="text/css">

    <!-- jQuery -->
    <script th:src="@{/assets/js/main/jquery.min.js}"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- /global stylesheets -->
    
    <style type="text/css">
        .logout-container {
            position: relative;
            width: 100%;
            padding: 10px 0;
            margin-bottom: 20px;
            min-height: 30px;
        }
        .logout-link {
            position: absolute;
            top: 10px;
            right: 10px;
            color: #dc3545;
            text-decoration: none;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            z-index: 10;
        }
        .logout-link:hover {
            color: #c82333;
            text-decoration: underline;
        }
        .logout-link i {
            margin-left: 5px;
        }
        .datatable-header {
            padding-top: 0px;
        }
        .error {
            color: #F44336;
        }
        /* Add CSS to control the logo size */
        .icon {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px; /* Optional: Adds spacing below the logo */
        }
        .icon img.left {
            max-width: 300px;
            height: auto; /* Maintain aspect ratio */
            width: 100%; /* Make it responsive */
        }
        @media (max-width: 768px) {
            .icon img.left {
                max-width: 150px; /* Smaller size for mobile */
            }
        }
        
        html, body {
            height: 100%; /* Ensure the body takes up full height */
            margin: 0; /* Remove default margin */
        }
        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh; /* Minimum height of the viewport */
        }
        main {
            flex: 1 0 auto; /* Allows the main content to grow and push the footer down */
        }
        .adaptive-footer {
            flex-shrink: 0; /* Prevents the footer from shrinking */
            width: 100%;
            background-color: #fff; /* Adjust to match your design */
        }

        .swal2-icon {
            font-size: 20px !important; /* Matches typical SweetAlert2 default */
            width: 80px !important;
            height: 80px !important;
            margin: 0 auto 20px auto !important;
        }
        .swal2-icon img {
            max-width: 100% !important;
            max-height: 100% !important;
        }
    </style>
</head>