
	<div id="divImportantNotes" class="hide">
		<ul id="importantNotesContent" th:name="${IMPORTANT_NOTES_TITLE}" th:utext="${IMPORTANT_NOTES_CONTENT}">
			
		</ul>
	</div>
	
	<!-- Footer -->
	<div th:fragment="page_footer" xmlns:th="http://www.w3.org/1999/xhtml" class="navbar navbar-expand-lg navbar-light">

		<div class="navbar-collapse" id="navbar-footer">
			<span class="navbar-text">
				&copy; [[${copyRightYear}]] <a href="http://www.eduhk.hk/" target="_blank">The Education University of Hong Kong</a> All Rights Reserved. <br> 
			</span>
			
			<ul class="navbar-nav">
				<li class="nav-item"><a id="jui-dialog-support-opener" class="nav-link"><i class="icon-lifebuoy mr-2"></i> Support</a></li>
			</ul>
		</div>

		<!-- dialog support -->
		<div id="jui-dialog-support" title="Support">
			<p><strong>Enquiry Hotline</strong><br>
				General Enquiries and Comments - 2948 6601</p>
			<p>OCIO Helpdesk<br>2948 6601</p>
		</div>
		<!-- /dialog support -->
	</div>
	<!-- /footer -->
	
	<div id="debugDiv" class="card hide">
		<div class="card-header header-elements-inline" style="padding-bottom:2px;">
			<span>DEBUG MSG</span>
		</div>

		<div class="card-body">
		</div>
	</div>
	
	<!-- Core JS files -->
	<script th:src="@{/assets/js/main/jquery.min.js}"></script>
	<script th:src="@{/assets/js/main/bootstrap.bundle.min.js}"></script>
	<script th:src="@{/assets/js/plugins/loaders/blockui.min.js}"></script>
	<script th:src="@{/assets/js/plugins/ui/slinky.min.js}"></script>
	<!-- /core JS files -->

	<!-- Theme JS files -->
	<script th:src="@{/assets/js/plugins/tables/datatables/datatables.min.js}"></script>
	<script th:src="@{/assets/js/plugins/tables/datatables/extensions/datatables.bootstrap4.js}"></script>
	<script th:src="@{/assets/js/plugins/tables/datatables/extensions/fixed_header.min.js}"></script>
	<script th:src="@{/assets/js/plugins/tables/datatables/extensions/responsive.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/styling/switchery.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/selects/bootstrap_multiselect.js}"></script>
	<script th:src="@{/assets/js/plugins/ui/moment/moment.min.js}"></script>
	<script th:src="@{/assets/js/plugins/pickers/daterangepicker.js}"></script>
	<script th:src="@{/assets/js/plugins/extensions/jquery_ui/widgets.min.js}"></script>
	<script th:src="@{/assets/js/plugins/extensions/jquery_ui/effects.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/inputs/inputmask.js}"></script>
	<script th:src="@{/assets/js/plugins/notifications/sweet_alert.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/selects/select2.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/styling/switchery.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/inputs/maxlength.min.js}"></script>
	<script th:src="@{/assets/js/plugins/notifications/jgrowl.min.js}"></script>
	<script th:src="@{/assets/js/plugins/notifications/noty.min.js}"></script>
	<script th:src="@{/assets/js/plugins/mustache/mustache.min.js}"></script>
	<script th:src="@{/assets/js/plugins/forms/validation/validate.min.js}"></script>
	<script th:src="@{/assets/js/plugins/uploaders/fileinput/fileinput.min.js}"></script>	
	<script th:src="@{/assets/js/plugins/file-saver/Blob.js}"></script>	
	<script th:src="@{/assets/js/plugins/file-saver/FileSaver.js}"></script>
	<script th:src="@{/assets/js/plugins/xlsx/xlsx.full.min.js}"></script>

	<!-- Add DataTables Buttons Extension -->
	<script th:src="@{/assets/js/plugins/tables/datatables/extensions/buttons.min.js}"></script>
	<script th:src="@{/assets/js/plugins/tables/datatables/extensions/buttons.html5.min.js}"></script>
	<!-- Optional: For Excel or PDF export -->
	 <script th:src="@{/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js}"></script>
	 <script th:src="@{/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js}"></script>
	 <script th:src="@{/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js}"></script>
	
	<script th:src="@{/assets/js/polyfills/promise.min.js}"></script>
	<script th:src="@{/assets/js/polyfills/fetch.min.js}"></script>
	
	<script th:src="@{/assets/js/app.js}"></script>
	<script th:src="@{/assets/js/custom.js}"></script>
	<!-- /theme JS files -->
	
	<script type="text/javascript">
	$.validator.addMethod("chosen",
			function(value, element) {
			    return (value == null ? false : (value.length == 0 ? false : true))
			}, "please select an option");
	$.validator.addMethod("dateFormat",
		    function(value, element) {
				var dateDDMMYYYYRegex = /((0[1-9]|[12]\d|3[01])-(0[1-9]|1[0-2])-[12]\d{3})/;
				return value == "" || dateDDMMYYYYRegex.test(value);
		    }, "Please enter a valid date in the format dd-mm-yyyy.");

    
	var viewport = document.querySelector("meta[name=viewport]");
	if (viewport) {
	    var content = viewport.getAttribute("content");
	    var parts = content.split(",");
	    for (var i = 0; i < parts.length; ++i) {
	        var part = parts[i].trim();
	        var pair = part.split("=");
	        if (pair[0] === "min-width") {
	            var minWidth = parseInt(pair[1]);
	        	var ww = ( $(window).width() < window.screen.width ) ? $(window).width() : window.screen.width;
	        	var ratio =  ww / minWidth;
	            if (screen.width < minWidth) {
	                document.head.removeChild(viewport);

	                var newViewport = document.createElement("meta");
	                newViewport.setAttribute("name", "viewport");
	                newViewport.setAttribute("content", "width=" + minWidth + ", initial-scale=" + ratio + ", shrink-to-fit=yes");
	                document.head.appendChild(newViewport);
	                break;
	            }
	        }
	    }
	}

	var swalInit = swal.mixin({
        buttonsStyling: false,
        confirmButtonClass: 'btn btn-primary',
        cancelButtonClass: 'btn btn-light'
    });

	$('#btnImportantNotes').on('click', function(e){
        if ($('#divImportantNotes').length > 0) {
			swalInit({
	            title: $('#importantNotesContent').attr('name'),
	            html: $('#divImportantNotes').html(),
	            showCloseButton: true,
	            showCancelButton: false,
	            focusConfirm: true,
	            confirmButtonText: '<i class="icon-check mr-1"></i> Okay',
	            confirmButtonAriaLabel: 'OK',
	            position: 'top',
	            width: '80%'
	        });
        }
	});

	</script>