	<!-- Main navbar -->
	<div th:fragment="navbar" xmlns:th="http://www.thymeleaf.org" class="navbar navbar-expand-lg navbar-dark"
		 style="background-color:#16a085 !important;background-size:contain; background-position: left; border-top: 3px solid #f4e536;border-bottom:0px none;"
		 xmlns:sec="http://www.w3.org/1999/xhtml">
		<div class="navbar-brand wmin-0 mr-1 d-flex">
			<a th:href="@{/}" class="d-inline-block" style="font-size:18px; color:#fff; float:left;">
				<img th:src="@{/assets/images/eduhk_logo.png}" style="width:45px; height:45px;" alt="EdUHK" class="left"/>
			</a>
			<div class="navbar-title" style="font-size:20px; float:left; line-height:45px; padding-left:16px; cursor:pointer;" th:onclick="window.location=[[@{/}]]" th:data-abbr="${systemNameAbbr}">
				<div style="font-size: 24px; float: left; color: #f4e536; font-weight: bold; font-style: italic; position: absolute; margin: 20px auto auto 48px;">[[${platformEnvironment}]]</div>
				<span>[[${systemName}]]</span>
			</div>
		</div>

		<div class="d-lg-none">
			<button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-mobile">
				<i class="icon-paragraph-justify3"></i>
			</button>
		</div>

		<div class="collapse navbar-collapse" id="navbar-mobile">

			<ul class="navbar-nav mr-auto">
				<li th:if="${isStaff}" class="nav-item"
					th:classappend="${#strings.startsWith(#request.servletPath, '/advisorInfo') ? 'active':''}">
					<a th:href="@{/advisorInfo}" sec:authorize-url="/" class="menu-item nav-link"><i class="icon-address-book"></i> My Advisees</a>
				</li><li th:if="${isStudent}" class="nav-item"
						 th:classappend="${#strings.startsWith(#request.servletPath, '/studentInfo') ? 'active':''}">
					<a th:href="@{/studentInfo}" sec:authorize-url="/" class="menu-item nav-link"><i class="icon-user"></i> My Profile</a>
				</li><li th:if="${isAdvisor}" class="nav-item"
						 th:classappend="${#strings.startsWith(#request.servletPath, '/advisorForm/submit') ? 'active':''}">
					<a th:href="@{/advisorForm/submit}" sec:authorize-url="/advisorForm/submit" class="menu-item nav-link">
						<i class="icon-file-text2"></i> Advising Meeting Records
					</a>
				</li><li th:if="${isStudent}" class="nav-item"
						 th:classappend="${#strings.startsWith(#request.servletPath, '/studentForm/submit') ? 'active':''}">
					<a th:href="@{/studentForm/submit}" sec:authorize-url="/studentForm/submit" class="menu-item nav-link">
						<i class="icon-file-text2"></i> Preparation for Advising Meeting
					</a>
				</li><li th:if="${isStudent}" class="nav-item"
						 th:classappend="${#strings.startsWith(#request.servletPath, '/studentForm/student') ? 'active':''}">
					<a th:href="@{/studentForm/student}" sec:authorize-url="/studentForm/student" class="menu-item nav-link">
						<i class="icon-history"></i> Meeting Records
					</a>
				</li>
			</ul>
			<ul class="navbar-nav m-2">
				<li class="nav-item text-right">
					<button type="button" class="btn btn-sm btn-light" data-toggle="modal" data-target="#logoutModal"><i class="icon-switch2"></i> Sign off</button>
				</li>
			</ul>
		</div>
		<div class="modal fade" id="logoutModal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-labelledby="logoutModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<form th:action="@{/logout}" method="post" class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="logoutModalLabel">Sign out</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						Are you sure to sign out?
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-dark" data-dismiss="modal">No</button>
						<button type="submit" class="btn btn-danger">Yes</button>
					</div>
				</form>
			</div>
		</div>
	</div>
	<!-- /main navbar -->

	<!-- login navbar -->
	<div th:fragment="login" xmlns:th="http://www.w3.org/1999/xhtml" class="navbar navbar-expand-lg navbar-dark" th:style="'background-color:#0696ad !important; background-image:url(' + @{/assets/images/backgrounds/circle_bg.png} + ')'">
		<div class="navbar-brand wmin-0 mr-1 d-flex">
			<a th:href="@{/}" class="d-inline-block" style="font-size:18px; color:#fff; float:left;">
				<img th:src="@{/assets/images/eduhk_logo.png}" style="width:45px; height:45px;" alt="EdUHK" class="left"/>
			</a>
			<div class="navbar-title" style="font-size:20px; float:left; line-height:45px; padding-left:16px; cursor:pointer;" th:onclick="window.location=[[@{/}]]" th:data-abbr="${systemName}">
				<div style="font-size: 24px; float: left; color: #f4e536; font-weight: bold; font-style: italic; position: absolute; margin: 20px auto auto 48px;">[[${platformEnvironment}]]</div>
				<span>[[${systemName}]]</span>
			</div>
		</div>
	</div>
	<!-- /login navbar -->

