<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>

<body class="page-login-index">
    <main>
        <section class="vsm-login-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 text-center mb-5"></div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-8 login-form">
                        <div class="login-wrap p-4 p-md-5">
                        <div th:replace="fragments/header :: header"></div>
                        <!-- Error Message Display -->
                        <div id="errorMessage" class="alert alert-danger text-center" style="display: none;"></div>

                        <!-- Back Button -->
                        <a href="javascript:history.back();" class="login-back-button">
                            <i class="fas fa-arrow-left"></i> Back
                        </a>
                        <h3 class="text-center mb-4">Student Login (without EdUHK account)</h3>

                        <div class="card-body ajaxForm">
                            <div id="container">
                                <!-- ID Type Selection -->
                                <div id="idSection">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label">Select ID Type</label>
                                        <div class="col-md-9">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="idType" id="sidOption" value="studentId">
                                                <label class="form-check-label" for="sidOption">Student ID</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="idType" id="hkidOption" value="hkid">
                                                <label class="form-check-label" for="hkidOption">HKID</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="idType" id="passportOption" value="passport">
                                                <label class="form-check-label" for="passportOption">Passport No.</label>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Input Fields for Selected ID -->
                                    <div class="form-group row" id="studentIdDiv" style="display: none;">
                                        <label for="studentId" class="col-md-3 col-form-label">Student ID</label>
                                        <div class="col-md-9">
                                            <input type="text" id="studentId" name="studentId" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="hkidDiv" style="display: none;">
                                        <label for="hkid" class="col-md-3 col-form-label">HKID</label>
                                        <div class="col-md-9">
                                            <input type="text" id="hkid" name="hkid" class="form-control" />
                                        </div>
                                    </div>
                                    <div class="form-group row" id="passportDiv" style="display: none;">
                                        <label for="passport" class="col-md-3 col-form-label">Passport No.</label>
                                        <div class="col-md-9">
                                            <input type="text" id="passport" name="passport" class="form-control" />
                                        </div>
                                    </div>

                                    <!-- Date of Birth (Mandatory) -->
                                    <div class="form-group row">
                                        <label for="dateOfBirth" class="col-md-3 col-form-label">Date of Birth</label>
                                        <div class="col-md-9">
                                            <input type="text" id="dateOfBirth" name="dateOfBirth" class="form-control" readonly placeholder="Select date"/>
                                        </div>
                                    </div>	  

                                    <div class="text-center"> 
                                        <button id="btnConfirm" type="button" class="btn btn-primary login-button" style="margin-top: 10px;width: 60%;">Login</button>
                                    </div>
                                </div>

                                <!-- Masked Phone Display & Send OTP -->
                                <div id="otpSection" style="display: none;">
                                    <div class="form-group row">
                                        <label class="col-md-3 col-form-label">Phone Number</label>
                                        <div class="col-md-9">
                                            <span id="maskedPhone" class="font-weight-bold"></span>
                                            <small class="form-text text-muted">
                                                A one-time password (OTP) will be sent to this registered phone number. If this number is incorrect, please update your contact details with the university.
                                            </small>
                                        </div>
                                    </div>
                                    <div class="text-center">
                                        <button id="btnSendOtp" type="button" class="btn btn-primary" style="margin-top: 10px;width: 60%;">Send OTP</button>
                                        <small class="form-text text-muted">
                                            Click the button to receive an OTP via SMS. The OTP will expire in <strong th:text="${T(com.eduhk.sa.config.Constant).OTP_VALIDITY_MINUTES} + ' minutes'"></strong>.
                                        </small>
                                    </div>
                                </div>

                                <!-- OTP Verification Section -->
                                <div id="otpVerifySection" style="display: none;">
                                    <form id="otpForm" th:action="@{/login/verifyOtp}" method="post">
                                        <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}" />
                                        <div class="form-group row">
                                            <label for="otpCode" class="col-md-3 col-form-label">Enter OTP</label>
                                            <div class="col-md-9">
                                                <input type="text" id="otpCode" name="otp" class="form-control" />
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <button id="btnVerifyOtp" type="submit" class="btn btn-success" style="margin-top: 10px;width: 60%;">Verify OTP</button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        </section>
    </main>

    <!-- Footer -->
    <div th:replace="fragments/page_footer"></div>

<script>
    $(document).ready(function() {
        $("#errorMessage").hide();
        
        $("#dateOfBirth").datepicker({
            dateFormat: "dd/mm/yy",
            changeMonth: true,
            changeYear: true,
            yearRange: "-100:+0",
            showButtonPanel: true
        });

        $("input[name='idType']").change(function() {
            $("#studentIdDiv, #hkidDiv, #passportDiv").hide();
            if ($("#sidOption").is(":checked")) {
                $("#studentIdDiv").show();
            } else if ($("#hkidOption").is(":checked")) {
                $("#hkidDiv").show();
            } else if ($("#passportOption").is(":checked")) {
                $("#passportDiv").show();
            }
        });

        $("#btnConfirm").on('click', function() {
            let idType = $("input[name='idType']:checked").val();
            let idNo = $("#" + idType).val().trim();
            let dob = $("#dateOfBirth").val().trim();

            // Validation
            if (!idType) {
                $("#errorMessage").text("Please select an ID type.").show();
                setTimeout(() => $("#errorMessage").hide(), 5000); // Hide after 5 seconds
                return;
            }
            if (!idNo) {
                $("#errorMessage").text("Please enter your " + getIdTypeLabel(idType) + ".").show();
                setTimeout(() => $("#errorMessage").hide(), 5000);
                return;
            }
            if (!dob) {
                $("#errorMessage").text("Please select your Date of Birth.").show();
                setTimeout(() => $("#errorMessage").hide(), 5000);
                return;
            }

            // Proceed with AJAX request if validation passes
            $.post("/[[${systemContextRoot}]]/login/nonEduhk", { idType, idNo, dateOfBirth: dob, "_csrf": "[[${_csrf.token}]]" })
                .done(response => {
                    if (response.status === "maskedPhone") {
                        $("#maskedPhone").text(response.maskedPhone);
                        $("#idSection").hide();
                        $("#otpSection").show();
                    } else {
                        $("#errorMessage").text(response.message).show();
                        setTimeout(() => $("#errorMessage").hide(), 5000);
                    }
                })
                .fail(() => {
                    $("#errorMessage").text("An error occurred while processing your request.").show();
                    setTimeout(() => $("#errorMessage").hide(), 5000);
                });
        });

        $("#btnSendOtp").on('click', function() {
            $.post("/[[${systemContextRoot}]]/login/sendOtp", { "_csrf": "[[${_csrf.token}]]" })
                .done(response => {
                    if (response.status === "otpSent") {
                        $("#otpSection").hide();
                        $("#otpVerifySection").show();
                    } else {
                        $("#errorMessage").text(response.message).show();
                        setTimeout(() => $("#errorMessage").hide(), 5000);
                    }
                })
                .fail(() => {
                    $("#errorMessage").text("An error occurred while sending OTP.").show();
                    setTimeout(() => $("#errorMessage").hide(), 5000);
                });
        });

        // Helper function to get user-friendly ID type label
        function getIdTypeLabel(idType) {
            switch (idType) {
                case "studentId": return "Student ID";
                case "hkid": return "HKID";
                case "passport": return "Passport Number";
                default: return "ID";
            }
        }
    });
</script>
</body>
</html>