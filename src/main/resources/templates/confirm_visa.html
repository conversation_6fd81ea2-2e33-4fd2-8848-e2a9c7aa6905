<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>
<link th:href="@{/assets/css/declaration.css}" rel="stylesheet" type="text/css">
<!-- Add SweetAlert2 for success notification -->
<!-- <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script> -->
<style>

    .form-group label {
        font-size: 0.875rem;
        font-weight: 600;
        color: #212529;
        margin-bottom: 0.5rem;
    }

    #errorMessage {
        display: none;
        font-size: 0.875rem;
        padding: 0.75rem;
        border-radius: 5px;
    }

    #confirmBtn {
        width: 100%;
    }
</style>
<body class="page-login-index">
    <main>
        <section class="vsm-login-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-8 login-form">
                        <div class="login-wrap p-4 p-md-5">
                            <div th:replace="fragments/header :: header"></div>
                            <div th:replace="fragments/logout :: logout"></div>
                            <h3 class="text-center mb-4">Confirmation of Visa Submission</h3>

                            <div class="row">
                                <div class="col-md-12">
                                    <div class="row">
                                        <!-- Student Details Card -->
                                        <div class="col-md-6 mb-4">
                                            <div class="info-card">
                                                <h5 class="info-card-title">Student Details</h5>
                                                <dl class="info-card-content">
                                                    <div class="info-item">
                                                        <dt>Student ID</dt>
                                                        <dd th:text="${visaInfo?.studentId}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Student Name</dt>
                                                        <dd th:text="${visaInfo?.studentName}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Programme</dt>
                                                        <dd th:text="${visaInfo?.programme}"></dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>
                                        <!-- Current Visa Details Card -->
                                        <div class="col-md-6 mb-4">
                                            <div class="info-card">
                                                <h5 class="info-card-title">Current Visa Details</h5>
                                                <dl class="info-card-content">
                                                    <div class="info-item">
                                                        <dt>Visa Type</dt>
                                                        <dd th:text="${visaInfo?.visaTypeDesc ?: 'N/A'}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Visa Number</dt>
                                                        <dd th:text="${visaInfo?.visaNumber ?: 'N/A'}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>VISA End Date</dt>
                                                        <dd th:text="${visaInfo?.visaExpiryDate != null ? #temporals.format(visaInfo?.visaExpiryDate, 'yyyy-MM-dd') : 'N/A'}"></dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Confirmation Details -->
                            <div class="info-card">
                                <h4 class="info-card-title">Submitted Visa Details</h4>

                                <input type="hidden" name="pidm" th:value="${visaUploadInfo.pidm}">
                                <input type="hidden" name="seqNo" th:value="${visaUploadInfo.seqNo}">
                                <input type="hidden" name="status" th:value="${visaUploadInfo.status}">

                                <dl class="info-card-content">
                                    <div class="info-item">
                                        <dt>Student Name</dt>
                                        <dd th:text="${visaUploadInfo?.studentName ?: 'N/A'}"></dd>
                                    </div>
                                    <div class="info-item">
                                        <dt>Institution Name</dt>
                                        <dd th:text="${visaUploadInfo?.institutionName ?: 'N/A'}"></dd>
                                    </div>
                                    <div class="info-item">
                                        <dt>Visa Type</dt>
                                        <dd th:text="${visaUploadInfo?.vtypeDesc ?: 'N/A'}"></dd>
                                    </div>
                                    <div class="info-item">
                                        <dt>Visa Number</dt>
                                        <dd th:text="${visaUploadInfo?.visaNumber ?: 'N/A'}"></dd>
                                    </div>
                                    <div class="info-item">
                                        <dt>VISA End Date</dt>
                                        <dd th:text="${visaUploadInfo?.visaExpiryDate != null ? #dates.format(visaUploadInfo?.visaExpiryDate, 'yyyy-MM-dd') : 'N/A'}"></dd>
                                    </div>
                                </dl>

                                <!-- Uploaded Files Preview -->
                                <div class="form-group">
                                    <label>Uploaded Files:</label>
                                    <div class="preview-container" id="previewContainer">
                                        <th:block th:each="file : ${visaUploadInfo?.uploadedFiles}">
                                            <div class="image-preview"
                                                 th:attr="data-filename=${file.filename}, data-base64='data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}">
                                                <th:block th:if="${file.mimetype.startsWith('image/')}">
                                                    <img th:src="'data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}"
                                                         class="preview-img" alt="Uploaded Image"/>
                                                </th:block>
                                                <th:block th:if="${file.mimetype == 'application/pdf'}">
                                                    <canvas class="pdf-preview"></canvas>
                                                </th:block>
                                            </div>
                                        </th:block>
                                    </div>
                                </div>
                            </div>

                            <!-- Error message display -->
                            <div id="errorMessage" class="alert alert-danger text-center"></div>.

                            <!-- Declaration Section -->
                            <th:block th:if="${visaUploadInfo?.status == 'DRAFT'}">
                                <div class="info-card mt-4">
                                    <h5 class="info-card-title">聲明 / 声明 / DECLARATION</h5>

                                    <div th:utext="${declarationContent}"></div>
    <!--                                <div class="vms-content">-->
    <!--                                    <h2 class="section-title">聲明 / 声明 / DECLARATION</h2>-->
    <!--                                    <div class="agreement-section">-->
    <!--                                        <div class="agreement-row">-->
    <!--                                            <input type="checkbox" id="agreementCheckbox" name="agreement" onchange="toggleConfirmButton()">-->
    <!--                                            <label for="agreementCheckbox" lang="ZH-TW">本人謹此聲明，上述所填報的資料全部屬實及正確無誤。</label>-->
    <!--                                        </div>-->
    <!--                                        <div class="agreement-row tight">-->
    <!--                                            <span class="checkbox-placeholder"></span>-->
    <!--                                            <label for="agreementCheckbox" lang="ZH-CN">本人谨此声明，上述所填报的数据全部属实及正确无误。</label>-->
    <!--                                        </div>-->
    <!--                                        <div class="agreement-row tight">-->
    <!--                                            <span class="checkbox-placeholder"></span>-->
    <!--                                            <label for="agreementCheckbox">I declare that all information entered above is true and correct to the best of my knowledge.</label>-->
    <!--                                        </div>-->
    <!--                                    </div>-->
                                </div>

                                <!-- Confirmation Buttons -->
                                <div class="text-center">
                                        <button type="button" id="confirmBtn" class="btn btn-primary action-button"
                                                disabled>Confirm Submission</button>
                                    <!-- Uncomment if needed -->
                                    <!-- <button type="button" id="editBtn" class="btn btn-secondary login-button"
                                            th:onclick="'window.location.href = \'' + @{/uploadVisa} + '\''">Edit Submission</button> -->
                                </div>
                            </th:block>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- Modals and scripts unchanged -->
<!--        <div id="imageModal" class="modal">-->
<!--            <span id="closeImageModal" class="modal-close">×</span>-->
<!--            <img id="modalImage" class="modal-content">-->
<!--        </div>-->
<!--        <div id="pdfModal" class="modal">-->
<!--            <span id="closePdfModal" class="modal-close">×</span>-->
<!--            <iframe id="pdfFrame" class="modal-content" style="width: 80%; height: 80%;"></iframe>-->
<!--        </div>-->
    </main>
    <div th:replace="fragments/page_footer"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
    $(document).ready(function () {
        initializePreloadedFiles();

        $(".preview-img").on("click", function () {
            var imgSrc = $(this).attr("src");
            $("#modalImage").attr("src", imgSrc);
            $("#imageModal").fadeIn();
        });

        $("#closeImageModal, #imageModal").on("click", function (event) {
            if (event.target === this) {
                $("#imageModal").fadeOut();
            }
        });

        $("#previewContainer").on("click", ".pdf-preview", function () {
            var pdfBlobURL = $(this).data("blob-url");
            $("#pdfFrame").attr("src", pdfBlobURL);
            $("#pdfModal").fadeIn();
        });

        $("#closePdfModal, #pdfModal").on("click", function (event) {
            if (event.target === this) {
                $("#pdfModal").fadeOut();
            }
        });

        $("#confirmBtn").on("click", function () {
            var pidm = $("input[name='pidm']").val();
            var seqNo = $("input[name='seqNo']").val();

            $.ajax({
                url: "/[[${systemContextRoot}]]/uploadVisa/finalizeSubmission?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                type: "POST",
                data: { pidm: pidm, seqNo: seqNo },
                success: function (response) {
                    Swal.fire({
                        title: "Success!",
                        text: "Visa submission finalized successfully!",
                        icon: "success",
                        confirmButtonText: "OK",
                        timer: 2000,
                        timerProgressBar: true
                    }).then(() => {
                        window.location.reload();
                    });
                },
                error: function (xhr, status, error) {
                    showError(xhr.responseText);
                }
            });
        });

        $("#editBtn").on("click", function () {
            var pidm = $("input[name='pidm']").val();
            var seqNo = $("input[name='seqNo']").val();

            $.ajax({
                url: "/[[${systemContextRoot}]]/uploadVisa/editSubmission?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                type: "POST",
                data: { pidm: pidm, seqNo: seqNo },
                success: function () {
                    window.location = "/[[${systemContextRoot}]]/uploadVisa/";
                },
                error: function (xhr, status, error) {
                    showError("Error editing submission: " + error);
                }
            });
        });

        // Checkbox toggle for Confirm button
        function toggleConfirmButton() {
            const checkbox = document.getElementById('agreementCheckbox');
            const confirmBtn = document.getElementById('confirmBtn');
            if (checkbox && confirmBtn) {
                confirmBtn.disabled = !checkbox.checked;
            }
        }

        // Expose toggleConfirmButton globally (for onchange)
        window.toggleConfirmButton = toggleConfirmButton;

        function initializePreloadedFiles() {
            $("#previewContainer .image-preview").each(function () {
                let fileName = $(this).attr("data-filename");
                let base64Data = $(this).attr("data-base64");

                if (!base64Data || !base64Data.startsWith("data:")) {
                    console.error("Invalid base64 data for:", fileName);
                    return;
                }

                let mimeType = base64Data.startsWith("data:image/") ? "image/jpeg" : "application/pdf";
                let base64Content = base64Data.split(",")[1];
                let blob = base64ToBlob(base64Content, mimeType);
                if (!blob) return;

                let file = new File([blob], fileName, { type: mimeType });

                if (mimeType === "application/pdf") {
                    renderPdfPreview(file, $(this));
                }
            });
        }

        function renderPdfPreview(file, fileWrapper) {
            let reader = new FileReader();
            reader.onload = function (e) {
                let pdfBlob = new Blob([e.target.result], { type: "application/pdf" });
                let pdfBlobURL = URL.createObjectURL(pdfBlob);

                let pdfCanvas = document.createElement("canvas");
                pdfCanvas.classList.add("pdf-preview");
                $(pdfCanvas).data("blob-url", pdfBlobURL);

                pdfjsLib.getDocument(pdfBlobURL).promise.then(pdf => {
                    pdf.getPage(1).then(page => {
                        let viewport = page.getViewport({ scale: 1.5 });
                        pdfCanvas.width = viewport.width;
                        pdfCanvas.height = viewport.height;
                        let renderContext = {
                            canvasContext: pdfCanvas.getContext("2d"),
                            viewport: viewport
                        };
                        page.render(renderContext).promise.then(() => {
                            fileWrapper.empty().append(pdfCanvas);
                        });
                    });
                });
            };
            reader.readAsArrayBuffer(file);
        }

        $('#previewContainer').on('click', '.pdf-preview', function(e) {
            e.preventDefault();
            const base64 = $(this).closest('.image-preview').attr('data-base64');
            if (base64 && base64.startsWith('data:application/pdf;base64,')) {
                const base64Content = base64.split(',')[1];
                const blob = base64ToBlob(base64Content, 'application/pdf');
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for PDF.");
                }
            }
        });

        $('#previewContainer').on('click', '.preview-img', function (e) {
            e.preventDefault();
            const imgSrc = $(this).attr('src');
            if (imgSrc && imgSrc.startsWith('data:image/')) {
                const mimeType = imgSrc.split(';')[0].split(':')[1];
                const base64Content = imgSrc.split(',')[1];
                const blob = base64ToBlob(base64Content, mimeType);
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for image.");
                }
            } else {
                console.error("Invalid image source:", imgSrc);
            }
        });

        function base64ToBlob(base64, mime) {
            try {
                const byteCharacters = atob(base64);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                return new Blob([new Uint8Array(byteNumbers)], { type: mime });
            } catch (error) {
                console.error("Error decoding Base64:", error);
                return null;
            }
        }

        function showError(message) {
            $("#errorMessage").text(message).fadeIn();
            setTimeout(() => $("#errorMessage").fadeOut(), 5000);
        }
    });
</script>
</body>
</html>