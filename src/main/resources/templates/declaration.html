<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400&family=Roboto:wght@400&display=swap" rel="stylesheet">
<link th:href="@{/assets/css/declaration.css}" rel="stylesheet" type="text/css">
<body class="page-login-index">
<main>
    <section class="vsm-login-section">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-8 login-form">
                    <div class="login-wrap p-4 p-md-5">
                        <div th:replace="fragments/header :: header"></div>
<!--                        <div th:replace="fragments/logout :: logout"></div>-->
                        <!-- Declaration Content -->
                        <div class="card-body">
<!--                            <h3 class="text-center mb-4">Visa Declaration</h3>-->
                            <div th:utext="${declarationContent}"></div>
<!--                            <div class="vms-content">-->
<!--                                <h2 class="section-title">Important Notes, Personal Information Collection Statement and Declaration for VMS</h2>-->

<!--                                <div class="language-group">-->
<!--                                    <p class="center" lang="ZH-TW">歡迎來到學生簽證管理系統（VMS）！</p>-->
<!--                                    <p class="tight" lang="ZH-TW">同學可於本系統上傳及遞交延長簽證/進入許可文件，包括延長簽證/進入許可通知書、入境標籤以及*往來港澳通行證「赴香港簽注」（逗留D）。</p>-->
<!--                                </div>-->

<!--                                <div class="language-group">-->
<!--                                    <p class="center" lang="ZH-CN">欢迎来到学生签证管理系统（VMS）！</p>-->
<!--                                    <p class="tight" lang="ZH-CN">同学可于本系统上传及递交延长签证/进入许可文件，包括延长签证/进入许可通知书、入境标签以及*往来港澳通行证「赴香港签注」（逗留D）。</p>-->
<!--                                </div>-->

<!--                                <div class="language-group">-->
<!--                                    <p class="center">Welcome to the Visa Management System (VMS) for Students!</p>-->
<!--                                    <p class="tight">This system allows you to upload and submit your extended visa/entry permit documents including extended visa/entry permit, landing slip and *Exit-Entry Permit.</p>-->
<!--                                </div>-->

<!--                                <p class="italic">*適用於內地學生 / 适用于内地学生 / applicable to Mainland students</p>-->

<!--                                <h3 class="section-title">重要說明 / 重要说明 / Important Notes</h3>-->
<!--                                <ol>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">同學於上傳及遞交簽證/進入許可文件時，務請提供最新資料。</p>-->
<!--                                        <p lang="ZH-CN">同学于上传及递交签证/进入许可文件时，务请提供最新数据。</p>-->
<!--                                        <p>Please ensure that the most up-to-date information is provided when you upload and submit your visa/entry permit documents.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">根據香港法例第200章《刑事罪行條例》，製作、提交或使用任何虛假文書（包括偽造文書）均屬嚴重刑事罪行。任何人觸犯與偽造或虛假文書有關的罪行，一經循公訴程序定罪，可被判處監禁。大學會考慮相關證據，向香港警方舉報可疑個案。對於在<u>入學前、入學期間及入學後</u>製作、偽造或使用虛假文書作為入學及任何其他用途的人士，大學亦會保留採取適當的法律行動的權利。</p>-->
<!--                                        <p lang="ZH-CN">根据香港法例第200章《刑事罪行条例》，制作、提交或使用任何虚假文书（包括伪造文书）均属严重刑事罪行。任何人触犯与伪造或虚假文书有关的罪行，一经循公诉程序定罪，可被判处监禁。大学会考虑相关证据，向香港警方举报可疑个案。对于在<u>入学前、入学期间及入学后</u>制作、伪造或使用虚假文书作为入学及任何其他用途的人士，大学亦会保留采取适当的法律行动的权利。</p>-->
<!--                                        <p>Making, submitting or using any false instrument (including forged documents) are serious criminal offences under the Crimes Ordinance (Cap. 200 of the laws of Hong Kong). A person committing the offences relating to forgery or false instruments is liable on conviction on indictment to imprisonment. The University shall consider the relevant evidence and report suspicious cases to the Hong Kong Police. The University reserves the right to take appropriate legal actions against persons who make, forge or use a false instrument for admission and any other academic purposes <strong>before, during and after the admission process</strong>.</p>-->
<!--                                    </li>-->
<!--                                </ol>-->

<!--                                <h3 class="section-title">收集個人資料聲明 / 收集个人资料声明 / Personal Information Collection Statement</h3>-->
<!--                                <ol>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">本系統所收集的資料將用以更新/處理有關學生之簽證狀況，所提供的資料將會轉存本校之學生紀錄。</p>-->
<!--                                        <p lang="ZH-CN">本系统所收集的资料将用以更新/处理有关学生之签证状况，所提供的资料将会转存本校之学生纪录。</p>-->
<!--                                        <p>The personal data provided by you on this system will be used for the purpose of updating/processing your visa/entry permit status and will be transferred to the student record system of the University after processing.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">若學生於本系統內提供的資料不足或不正確，本校可能無法處理有關資料。</p>-->
<!--                                        <p lang="ZH-CN">若学生于本系统内提供的数据不足或不正确，本校可能无法处理有关资料。</p>-->
<!--                                        <p>Failure to provide complete and accurate information on this system may result in the University being unable to process the information.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">本系統所收集的資料絕對保密。如有需要，將會轉交其他行政或教學部門作考慮或批核用途。</p>-->
<!--                                        <p lang="ZH-CN">本系统所收集的资料绝对保密。如有需要，将会转交其他行政或教学部门作考虑或批核用途。</p>-->
<!--                                        <p>Information provided will be treated strictly confidential and may be transferred to other unit(s) within the University for necessary action, where applicable.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">非本地生若其學生紀錄有所更改，有關更改如有需要亦會通知/轉交相關政府部門（例如：香港特別行政區政府入境事務處）。</p>-->
<!--                                        <p lang="ZH-CN">非本地生若其学生纪录有所更改，有关更改如有需要亦会通知/转交相关政府部门（例如：香港特别行政区政府入境事务处）。</p>-->
<!--                                        <p>For non-local students, changes in student record may also be disclosed/transferred to relevant Government bodies, such as the Immigration Department of the Government of the Hong Kong Special Administrative Region, if deemed necessary.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">如需在遞交簽證/進入許可文件後查閱或更正個人資料，請聯絡教務處（電郵：<a href="mailto:<EMAIL>"><EMAIL></a>）提出書面申請。</p>-->
<!--                                        <p lang="ZH-CN">如需在递交签证/进入许可文件后查阅或更正个人资料，请联络教务处（电邮：<a href="mailto:<EMAIL>"><EMAIL></a>）提出书面申请。</p>-->
<!--                                        <p>Applications for access to and correction of personal data after submitting the visa/entry permit documents should be made by writing to Registry by email to <a href="mailto:<EMAIL>"><EMAIL></a>.</p>-->
<!--                                    </li>-->
<!--                                    <li class="point">-->
<!--                                        <p lang="ZH-TW">本校的「私隱政策聲明」可見於 <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/main/privacy-policy/">https://www.eduhk.hk/main/privacy-policy/</a>。</p>-->
<!--                                        <p lang="ZH-CN">本校的「私隐政策声明」可见于 <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/main/privacy-policy/">https://www.eduhk.hk/main/privacy-policy/</a>。</p>-->
<!--                                        <p>The University’s Privacy Policy Statement (PPS) can be accessed at <a target="_blank" rel="noopener noreferrer" href="https://www.eduhk.hk/main/privacy-policy/">https://www.eduhk.hk/main/privacy-policy/</a>.</p>-->
<!--                                    </li>-->
<!--                                </ol>-->

<!--                                <div class="agreement-section">-->
<!--                                    <div class="agreement-row">-->
<!--                                        <input type="checkbox" id="agreementCheckbox" name="agreement" onchange="toggleUnderstandButton()">-->
<!--                                        <label for="agreementCheckbox" lang="ZH-TW">我已閱讀並同意遵守上述內容。</label>-->
<!--                                    </div>-->
<!--                                    <div class="agreement-row tight">-->
<!--                                        <span class="checkbox-placeholder"></span>-->
<!--                                        <label for="agreementCheckbox" lang="ZH-CN">我已阅读并同意遵守上述内容。</label>-->
<!--                                    </div>-->
<!--                                    <div class="agreement-row tight">-->
<!--                                        <span class="checkbox-placeholder"></span>-->
<!--                                        <label for="agreementCheckbox">I have read and agreed to abide by the above.</label>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
                            <!-- Buttons -->
                            <div class="button-container d-flex justify-content-center mt-4">
                                <button class="confirm-btn btn btn-primary mr-2" name="btnAgree" id="understandBtn" onclick="onClickUnderstand()" disabled>I Understand</button>
                                <button class="exit-btn btn btn-secondary" name="btnExit" onclick="onClickBack()">Back</button>
                            </div>
                            <form th:action="@{/logout}" method="post" id="logoutForm" style="display: none;"></form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>
<div th:replace="fragments/page_footer"></div>

<script>
    $(document).ready(function () {
        toggleUnderstandButton();
    });

    function toggleUnderstandButton() {
        const checkbox = document.getElementById('agreementCheckbox');
        const understandBtn = document.getElementById('understandBtn');
        if (checkbox && understandBtn) {
            understandBtn.disabled = !checkbox.checked;
        }
    }

    function onClickUnderstand() {
        let redirectUrl = "/[[${systemContextRoot}]]/uploadVisa";
        window.location.href = redirectUrl;
    }

    function onClickBack() {
        let $form = $('#logoutForm');
        if ($form.length) {
            $form.submit();
        } else {
            console.error("Logout form not found in DOM!");
        }
    }
</script>
</body>
</html>