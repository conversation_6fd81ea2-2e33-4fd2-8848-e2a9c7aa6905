<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>

<body class="page-login-index">
    <main>
        <section class="vsm-login-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-8 login-form">
                        <div class="login-wrap p-4 p-md-5">
                            <div th:replace="fragments/header :: header"></div>
                            <div class="justify-content-center">
                                <button type="button" class="btn btn-primary login-button" id="btn_normal_login"><span class="far fa-user btn-icon"></span>Login with EdUHK Network Account</button>
                                <button type="button" class="btn btn-primary login-button" id="btn_non_normal_login"><span class="far fa-user btn-icon"></span>Login without EdUHK Network Account (Student Only)</button>
                            </div>
                            <div style="text-align: right;">
                                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                    <span>User Guide</span>
                                </a>
                                <div class="dropdown-menu dropdown-menu-left">
                                    <a th:href="@{/pdf/Student VMS user guide V.3 (ENG).pdf}" target="_blank" class="dropdown-item">English</a>
                                    <a th:href="@{/pdf/Student VMS user guide V.2 (簡體).pdf}" target="_blank" class="dropdown-item">简体</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <div th:replace="fragments/page_footer"></div>
    <!-- /footer -->

    <script type="text/javascript">
        $(document).ready(function() {
            $('#btn_normal_login').click(function(event){
                event.preventDefault();
                window.location.href = "/[[${systemContextRoot}]]/login/eduhk/";
            });

            $('#btn_non_normal_login').click(function(event){
                event.preventDefault();
                window.location.href = "/[[${systemContextRoot}]]/login/nonEduhk/";
            });
        });
    </script>
</body>
</html>