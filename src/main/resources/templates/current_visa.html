<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>
<style>
    /* Reuse styles from visa_upload.html */
    body {
        background: #f8f9fd;
    }
</style>
<body class="page-login-index">
    <main>
        <section class="vsm-login-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 text-center mb-5">
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-8 login-form">
                        <div class="login-wrap p-4 p-md-5">
                            <div th:replace="fragments/header :: header"></div>
                            <div th:replace="fragments/logout :: logout"></div>
                            <!-- Error message display -->
                            <div th:if="${errorMessage}" class="alert alert-danger text-center">
                                <p th:text="${errorMessage}"></p>
                            </div>

                            <!-- Visa Info Display Section -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="row">
                                        <!-- Student Details Card -->
                                        <div class="col-md-6 mb-4">
                                            <div class="info-card">
                                                <h5 class="info-card-title">Student Details</h5>
                                                <dl class="info-card-content">
                                                    <div class="info-item">
                                                        <dt>Student ID</dt>
                                                        <dd th:text="${visaInfo?.studentId}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Student Name</dt>
                                                        <dd th:text="${visaInfo?.studentName}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Programme</dt>
                                                        <dd th:text="${visaInfo?.programme}"></dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>
                                        <!-- Current Visa Details Card -->
                                        <div class="col-md-6 mb-4">
                                            <div class="info-card">
                                                <h5 class="info-card-title">Current Visa Details</h5>
                                                <dl class="info-card-content">
                                                    <div class="info-item">
                                                        <dt>Visa Type</dt>
                                                        <dd th:text="${visaInfo?.visaTypeDesc ?: 'N/A'}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>Visa Number</dt>
                                                        <dd th:text="${visaInfo?.visaNumber ?: 'N/A'}"></dd>
                                                    </div>
                                                    <div class="info-item">
                                                        <dt>VISA End Date</dt>
                                                        <dd th:text="${visaInfo?.visaExpiryDate != null ? #temporals.format(visaInfo?.visaExpiryDate, 'yyyy-MM-dd') : 'N/A'}"></dd>
                                                    </div>
                                                </dl>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- No Visa Information Available Message -->
                            <div th:if="${visaUploadInfo == null and visaInfo == null}" class="alert alert-warning text-center">
                                <p>No visa information available.</p>
                            </div>

                            <div class="info-card">
                                <h4 class="info-card-title">Submitted Visa Details</h4>
                                <!-- Visa Upload Files Section (Only show if visaUploadInfo has uploaded files) -->
                                <div th:if="${visaUploadInfo != null and not #lists.isEmpty(visaUploadInfo.uploadedFiles)}">
                                    <!-- Uploaded Files Preview -->
                                    <div class="form-group">
                                        <label><strong>Uploaded Files:</strong></label>
                                        <div class="preview-container" id="previewContainer">
                                            <th:block th:each="file : ${visaUploadInfo?.uploadedFiles}">
                                                <div class="image-preview" th:attr="data-filename=${file.filename},data-base64='data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}">
                                                    <th:block th:if="${file.mimetype.startsWith('image/')}">
                                                        <img th:src="'data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}"
                                                            class="preview-img" alt="Uploaded Image"/>
                                                    </th:block>
                                                    <th:block th:if="${file.mimetype == 'application/pdf'}">
                                                        <canvas class="pdf-preview"></canvas>
                                                    </th:block>
                                                </div>
                                            </th:block>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

<!-- Footer -->
<div th:replace="fragments/page_footer"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
    $(document).ready(function () {
        // Configure pdf.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';

        // // Show Image Modal
        // $(".preview-img").on("click", function () {
        //     var imgSrc = $(this).attr("src");
        //     $("#modalImage").attr("src", imgSrc);
        //     $("#imageModal").fadeIn();
        // });
        //
        // // Close Image Modal
        // $("#closeImageModal, #imageModal").on("click", function (event) {
        //     if (event.target === this) {
        //         $("#imageModal").fadeOut();
        //     }
        // });
        //
        // // Close PDF Modal
        // $("#closePdfModal, #pdfModal").on("click", function (event) {
        //     if (event.target === this) {
        //         $("#pdfModal").fadeOut();
        //     }
        // });

        // Render PDF previews
        $(".image-preview").each(function () {
            let base64Data = $(this).attr("data-base64");
            let canvas = $(this).find(".pdf-preview")[0];
            if (canvas && base64Data && base64Data.startsWith("data:application/pdf;base64,")) {
                renderPdfPreview(base64Data, canvas, this);
            }
        });
    });

    // Function to render PDF preview
    function renderPdfPreview(base64Data, canvas, previewDiv) {
        let pdfBlob = base64ToBlob(base64Data.split(",")[1], "application/pdf");
        if (!pdfBlob) {
            console.error("Failed to create blob from base64 data");
            return;
        }
        
        let pdfBlobURL = URL.createObjectURL(pdfBlob);
        
        // Load PDF and render thumbnail
        pdfjsLib.getDocument(pdfBlobURL).promise.then(pdf => {
            pdf.getPage(1).then(page => {
                // Calculate scale to fit within 100x100px
                let viewport = page.getViewport({ scale: 1 });
                let scale = Math.min(100 / viewport.width, 100 / viewport.height);
                let scaledViewport = page.getViewport({ scale: scale });

                canvas.width = scaledViewport.width;
                canvas.height = scaledViewport.height;

                let renderContext = {
                    canvasContext: canvas.getContext("2d"),
                    viewport: scaledViewport
                };

                // Render the page
                page.render(renderContext).promise.then(() => {
                    // console.log("PDF thumbnail rendered successfully for:", $(previewDiv).attr("data-filename"));
                    $(canvas).on("click", function () {
                        $("#pdfFrame").attr("src", pdfBlobURL);
                        $("#pdfModal").fadeIn();
                    });
                }).catch(error => {
                    console.error("Error rendering PDF page:", error);
                });
            }).catch(error => {
                console.error("Error getting PDF page:", error);
            });
        }).catch(error => {
            console.error("Error loading PDF document:", error);
        });
    }

    $('#previewContainer').on('click', '.pdf-preview', function(e) {
        e.preventDefault();
        const base64 = $(this).closest('.image-preview').attr('data-base64');
        if (base64 && base64.startsWith('data:application/pdf;base64,')) {
            const base64Content = base64.split(',')[1];
            const blob = base64ToBlob(base64Content, 'application/pdf');
            if (blob) {
                const blobUrl = URL.createObjectURL(blob);
                window.open(blobUrl, '_blank');
            } else {
                console.error("Failed to create blob for PDF.");
            }
        }
    });

    $('#previewContainer').on('click', '.preview-img', function (e) {
        e.preventDefault();
        const imgSrc = $(this).attr('src');
        if (imgSrc && imgSrc.startsWith('data:image/')) {
            const mimeType = imgSrc.split(';')[0].split(':')[1];
            const base64Content = imgSrc.split(',')[1];
            const blob = base64ToBlob(base64Content, mimeType);
            if (blob) {
                const blobUrl = URL.createObjectURL(blob);
                window.open(blobUrl, '_blank');
            } else {
                console.error("Failed to create blob for image.");
            }
        } else {
            console.error("Invalid image source:", imgSrc);
        }
    });

    // Function to convert base64 to blob
    function base64ToBlob(base64, mime) {
        try {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            return new Blob([new Uint8Array(byteNumbers)], { type: mime });
        } catch (error) {
            console.error("Error decoding Base64:", error);
            return null;
        }
    }
</script>

</body>
</html>