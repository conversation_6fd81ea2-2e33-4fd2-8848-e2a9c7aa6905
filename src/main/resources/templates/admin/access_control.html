<!DOCTYPE html>
<html lang="en">
<head th:include="fragments/admin_head :: head">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, min-width=480">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>[[${systemName}]]</title>

    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/16x16.png}" sizes="16x16" />
    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/32x32.png}" sizes="32x32" />
    <link rel="shortcut icon" type="image/png" th:href="@{/assets/images/favicon/96x96.png}" sizes="96x96" />

    <!-- Global stylesheets -->
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,300,100,500,700,900" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/icons/icomoon/styles.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/icons/fontawesome/styles.min.css}" rel="stylesheet" type="text/css">    
    <link th:href="@{/assets/css/vendor/bootstrap.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/vendor/bootstrap_limitless.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/layout.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/components.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/colors.min.css}" rel="stylesheet" type="text/css">
    <link th:href="@{/assets/css/myStyles.css}" rel="stylesheet" type="text/css">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link th:href="@{/assets/css/view_aap_list.css}" rel="stylesheet" type="text/css">
</head>
<style>
    .select2-container {
        width: 100% !important;
    }
    .select2-dropdown {
        z-index: 1051;
        border: 1px solid #aaa;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    .select2-container--open .select2-dropdown {
        display: block !important;
    }
    .modal {
        overflow: visible !important;
    }
    .modal-dialog {
        overflow: visible !important;
    }
    .modal-body {
        overflow-y: auto;
        max-height: 65vh;
        position: relative;
    }
    .select2-selection--multiple .select2-selection__rendered {
        padding: 0.375rem 0.75rem;
        line-height: 1.5;
    }
    .select2-selection {
        height: calc(1.5em + 0.75rem + 2px);
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
    }
</style>
<body>
    <!-- Main navbar -->
    <div th:replace="fragments/admin_navbar :: navbar"></div>
    <!-- /main navbar -->

    <!-- Page header -->
    <div class="page-header">
        <div class="page-header-content header-elements-md-inline">
            <div class="page-title d-flex">
                <h4><i class="icon-compose mr-2"></i> <span class="font-weight-semibold">Access Right Control</span></h4>
                <a href="#" class="header-elements-toggle text-default d-md-none navbar-toggler sidebar-mobile-component-toggle"><i class="icon-more"></i></a>
            </div>

            <div class="header-elements d-none py-0 mb-3 mb-md-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{'/'}"><i class="icon-home2 mr-2"></i> Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Access Right Control</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <!-- /page header -->

    <!-- Page content -->
    <div class="page-content pt-0">
        <div class="content-wrapper">
            <div class="content">
                <div class="d-md-flex align-items-md-start">
                    <div class="card tab-content w-100 overflow-auto">
                        <div class="card-header header-elements-inline">
                            <h5 class="card-title"><i class="icon-filter4 mr-2"></i> User List</h5>
                            <div class="header-elements">
                                <div class="float-right">
                                    <button id="btnShowAddForm" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#modal_form_NewUser"><i class="icon-plus3 mr-2"></i> Add New User</button>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <table id="tableUserList" class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Full Name</th>
                                        <th>Network ID</th>
                                        <th>Department</th>
                                        <th>Role</th>
                                        <th>Start Date</th>
                                        <th>End Date</th>
                                        <th>Status</th>
                                        <th class="text-center">Actions</th>
                                    </tr>
                                </thead>
                                <tbody></tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /page content -->

    <!-- Add New User modal -->
    <div id="modal_form_NewUser" class="modal fade" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="close" data-dismiss="modal">×</button>
                </div>
                <form id="frmUser" action="#" class="form-horizontal">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Role</label>
                            <div class="col-sm-9">
                                <select id="user_role" name="user_role" class="form-control select">
                                    <option value="">Please Select</option>
                                    <option th:each="eachRole : ${roleList}" th:value="${eachRole.code}" th:text="${eachRole.description}"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Name</label>
                            <div class="col-sm-9">
                                <select id="user_staffNetworkID" name="user_staffNetworkID" class="form-control"></select>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Program Code</label>
                            <div class="col-sm-9">
                                <select id="user_dept_prog_code" name="user_dept_prog_code" class="form-control select" multiple="multiple">
                                    <option value="">Please Select</option>
                                </select>
                            </div>
                            <div class="col-sm-3">
                            </div>
                            <div class="col-sm-9">
                                <input type="checkbox" id="allProgCheckbox" name="allProg" onchange="toggleAllProgCheckboxBtn('CREATE')">
                                <label for="allProgCheckbox">All Programme</label>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Start Date</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control daterange-single" id="user_periodStartDateNew" name="user_periodStartDate" data-mask="9999-99-99" placeholder="YYYY-MM-DD">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">End Date</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control daterange-single" id="user_periodEndDateNew" name="user_periodEndDate" data-mask="9999-99-99" placeholder="YYYY-MM-DD">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn bg-primary" id="btnAddNew">Add</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /Add New User modal -->

    <!-- Edit User modal -->
    <div id="modal_form_EditUser" class="modal fade" tabindex="-1">
        <!-- <div class="modal-dialog modal-lg">
            <div class="modal-content"></div>
        </div> -->
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User</h5>
                    <button type="button" class="close" data-dismiss="modal">×</button>
                </div>
                <form id="frmUserEdit" action="#" class="form-horizontal">
                    <div class="modal-body">
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Role</label>
							<div class="col-sm-9">
								<input type="text" class="form-control" name="user_role" value="user_role" disabled>
							</div>
                        </div>
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Name</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control" name="user_staffNetworkID" value="user_staffNetworkID" disabled>
                            </div>
                        </div>

                        <div id="edit_prog_code_row">
                            <div class="form-group row" >
                                <label class="col-form-label col-sm-3">Program Code</label>
                                <div class="col-sm-9">
                                    <select id="editDeptProgCode" name="deptProgCode" class="form-control select" multiple="multiple">
                                        <option value="">Please Select</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" >
                                <div class="col-sm-3">
                                </div>
                                <div class="col-sm-9">
                                    <input type="checkbox" id="editAllProgCheckbox" name="editAllProg" onchange="toggleAllProgCheckboxBtn('UPDATE')">
                                    <label for="editAllProgCheckbox">All Programme</label>
                                </div>
                            </div>
                        </div>



                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">Start Date</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control daterange-single" id="user_periodStartDateNew" name="user_periodStartDate" data-mask="9999-99-99" placeholder="YYYY-MM-DD">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-form-label col-sm-3">End Date</label>
                            <div class="col-sm-9">
                                <input type="text" class="form-control daterange-single" id="user_periodEndDateNew" name="user_periodEndDate" data-mask="9999-99-99" placeholder="YYYY-MM-DD">
                            </div>
                        </div>
                        <div class="form-group row">
							<label class="col-form-label col-sm-3">Status</label>
							<div class="col-md-auto">
								<div class="custom-control custom-radio custom-control-inline">
									<input type="radio" class="custom-control-input" name="user_status" id="user_status_active" value="A">
									<label class="custom-control-label " for="user_status_active">Active</label>
								</div>
								<div class="custom-control custom-radio custom-control-inline">
									<input type="radio" class="custom-control-input " name="user_status" id="user_status_inactive" value="I">
									<label class="custom-control-label " for="user_status_inactive" style="padding-right: 8px; white-space: nowrap;">Inactive </label>
								</div>
							</div>
						</div>
                        <div class="form-group row">
                            <div class="col-sm-9">
                                <input type="hidden" name="networkId" value="networkId" disabled>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-sm-9">
                                <input type="hidden" name="roleCode" value="roleCode" disabled>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-link" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn bg-primary" id="btnEdit">Save</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- /Edit User modal -->

    <!-- Footer -->
    <div th:replace="fragments/admin_page_footer"></div>
    <!-- /footer -->

    <!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script th:src="@{/assets/js/datatables_extension_fixed_header.js}"></script>

<script th:inline="javascript">
        /*<![CDATA[*/
    // Convert Java collections to JavaScript arrays
    var currUser = /*[[${ssoUserCn}]]*/ "";
    var currUserPower = /*[[${currUserRole.power}]]*/ "";
    var currUserIsSysAdmin = /*[[${T(com.eduhk.sa.model.accessControl.Role).CODE_REG_USER.equals(currUserRole.code)}]]*/ "";

    var systemContextRoot = /*[[${systemContextRoot}]]*/ '';
    var csrfParameterName = /*[[${_csrf.parameterName}]]*/ '';
    var csrfToken = /*[[${_csrf.token}]]*/ '';
    var newLerList = /*[[${newLerList}]]*/ [];
    var newLerDescList = /*[[${newLerDescList}]]*/ [];

    let roleList = /*[[${roleList}]]*/ [];
        // var currUser = /*[[${ssoUserCn}]]*/ '';
    // var currUserPower = /*[[${currUserRole.power}]]*/ '';
    // var currUserIsSysAdmin = /*[[${T(com.eduhk.sa.model.accessControl.Role).CODE_REG_USER.equals(currUserRole.code)}]]*/ '';

    /*]]>*/

function initStaffSelect() {
    $.ajax({
        type: "get",
        url: `/${systemContextRoot}/admin/ajax/accessControl/stafflist?${csrfParameterName}=${csrfToken}`,
        success: function(data) {
            $("#user_staffNetworkID").empty(); // Clear existing options
            $("#user_staffNetworkID").append('<option value=""> </option>'); // Add blank option for placeholder
            $.each(data, function(i, deptStaffList) {
                var $optGroup = $("<optgroup>").attr("label", deptStaffList.code);
                $.each(deptStaffList.staffList, function(i, staffList) {
                    $optGroup.append(
                        $("<option>")
                            .val(staffList.cn)
                            .text(`${staffList.lastName} ${staffList.firstName} (${staffList.cn})`)
                            .attr("data-keywords", staffList.cn)
                            .attr("data-institute-id", staffList.instituteId)
                    );
                });
                $("#user_staffNetworkID").append($optGroup);
            });
            $("#user_staffNetworkID").select2({
                width: "100%",
                placeholder: "Enter Staff Name / Network ID",
                allowClear: true,
                dropdownParent: $("#modal_form_NewUser"),
                matcher: function(params, data) {
                    // If no search term, return all options
                    if ($.trim(params.term) === '') {
                        return data;
                    }
                    var term = params.term.toLowerCase();

                    // If data has children (e.g., optgroup), filter them
                    if (data.children && data.children.length > 0) {
                        var filteredChildren = [];
                        $.each(data.children, function(index, child) {
                            var text = child.text.toLowerCase();
                            var id = child.id.toLowerCase();

                            // Split text into name and ID
                            var match = text.match(/^(.*)\s\((.*?)\)$/);
                            var name = match ? match[1] : text;
                            var childId = match ? match[2] : id;

                            // Match term against name or ID
                            if (name.indexOf(term) > -1 || childId.indexOf(term) > -1) {
                                filteredChildren.push(child);
                            }
                        });

                        // Return a copy of data with filtered children
                        if (filteredChildren.length > 0) {
                            var newData = $.extend({}, data);
                            newData.children = filteredChildren;
                            return newData;
                        }
                        return null;
                    }

                    // If data is a single option (no children), filter directly
                    var text = data.text ? data.text.toLowerCase() : '';
                    var id = data.id ? data.id.toLowerCase() : '';
                    var match = text.match(/^(.*)\s\((.*?)\)$/);
                    var name = match ? match[1] : text;
                    var dataId = match ? match[2] : id;

                    return (name.indexOf(term) > -1 || dataId.indexOf(term) > -1) ? data : null;
                }
            }).val(null).trigger('change'); // Ensure cleared after initialization
        },
        error: function(e) {
            console.log("AJAX Error: ", e);
        }
    });
}

function toggleAllProgCheckboxBtn(action){
    let checkbox = null;
    let programSelect = null;
    if(action === "CREATE"){
        checkbox = document.getElementById('allProgCheckbox');
        programSelect = $('#user_dept_prog_code');
    }else if(action === "UPDATE"){
        checkbox = document.getElementById('editAllProgCheckbox');
        programSelect = $('#editDeptProgCode');
    }
    if(checkbox !==null && programSelect !== null){
        if (checkbox.checked) {
            // Clear all selected options
            programSelect.val(null).trigger('change');

            // Disable the dropdown
            programSelect.prop('disabled', true);
        } else {
            // Enable the dropdown
            programSelect.prop('disabled', false);
        }
    }
}
$(document).ready(function() {

    // Initialize DataTable
    var table = $('#tableUserList').DataTable({
        responsive: true,
        autoWidth: false,
        ajax: `/${systemContextRoot}/admin/ajax/accessControl/user/list?${csrfParameterName}=${csrfToken}`,
        columns: [
            { data: null, render: function(data, type, row) { return row.staffUser != null ? row.staffUser.lastName + ' ' + row.staffUser.firstName : "-"; } },
            { data: "cn" },
            { data: null, render: function(data, type, row) { return row.staffUser != null ? row.staffUser.department : "-"; } },
            { data: "role.description" },
            { data: null, render: function(data, type, row) { return row.periodStartDate == null ? '-' : moment(row.periodStartDate).utcOffset('+08:00').format('YYYY-MM-DD'); } },
            { data: null, render: function(data, type, row) { return row.periodEndDate == null ? '-' : moment(row.periodEndDate).utcOffset('+08:00').format('YYYY-MM-DD'); } },
            { data: "status", render: function(data, type, row) { return row.status == 'A' ? '<span class="badge badge-success">Active</span>' : '<span class="badge badge-secondary">Inactive</span>'; } },
            { data: null, render: function(data, type, row) { 
                return !currUserIsSysAdmin && (row.cn == currUser || row.role.power >= currUserPower) ? '' : 
                    '<div class="list-icons">' +
                    '    <a href="#" class="list-icons-item text-info-600 btnEditUser" data-role="'+data.role.code+'" title="Edit"><i class="icon-pencil7"></i></a>' +
                    '    <a href="#" class="list-icons-item text-danger-600 btnDeleteUser" data-role="'+data.role.code+'" title="Delete"><i class="icon-cross2"></i></a>' +
                    '</div>'; 
            }}
        ]
    });
    table.on('draw', function() {
        $('tr td:nth-child(8)').addClass('text-center');
    });

    // Show Add New User modal
    $('#btnShowAddForm').click(function(event) {
        console.log(`on click add form`)
        $('#modal_form_NewUser').modal('show');
    });

    // Initialize and reset dropdowns when modal is shown
    $('#modal_form_NewUser').on('shown.bs.modal', function () {
        // Reset the form
        $('#frmUser')[0].reset();

        // Initialize and clear Role dropdown
        $('#user_role option').prop('selected', false);
        $('#user_role').select2({
            minimumResultsForSearch: Infinity,
            placeholder: "Please Select",
            allowClear: true
        }).val(null).trigger('change');
        // Initialize and clear Staff dropdown
        $("#user_staffNetworkID").empty();
        $("#user_staffNetworkID").append('<option value=""> </option>');
        initStaffSelect();
    });

    // Reset on modal close
    $('#modal_form_NewUser').on('hidden.bs.modal', function () {
        $('#frmUser')[0].reset();
        $('#user_role').val(null).trigger('change');
        $('#user_staffNetworkID').val(null).trigger('change');
    });

    // Add New User submission
    $('#modal_form_NewUser button#btnAddNew').click(function(event) {
        event.preventDefault();
        showOverlayLoading();

        let $form = $(this).closest('form');
        let formData = readNewUserForm($form);

        if (!formData.role.code) {
            closeOverlayLoading();
            alert("Please select the user role");
            return;
        }
        if (!formData.cn) {
            closeOverlayLoading();
            alert("Please select a staff");
            return;
        }
        if (!formData.periodStartDate || isNaN(formData.periodStartDate.valueOf())) {
            closeOverlayLoading();
            alert("Please select a valid start date");
            return;
        }
        $.ajax({
            type: "post",
            contentType: "application/json",
            url: `/${systemContextRoot}/admin/ajax/accessControl/userRole/save?${csrfParameterName}=${csrfToken}`,
            data: JSON.stringify(formData),
            dataType: "json",
            cache: false,
            timeout: 600000,
            success: function(data) {
                closeOverlayLoading();
                new Noty({
                    text: '<i class="icon-checkmark-circle icon-2x mr-2"></i> Saved Successfully!',
                    type: 'success',
                    layout: 'center',
                    modal: true
                }).show();
                window.location.reload();
            },
            error: function(e, textStatus, errorThrown) {
                var json = "<h4>Ajax Response</h4><pre>" + e.responseText + "</pre>";
                $('#debugDiv .card-body').html(json);
                console.log("ERROR : ", e);
                console.log("textStatus : ", textStatus);
                console.log("errorThrown : ", errorThrown);
                closeOverlayLoading();
                $('html, body').animate({ scrollTop: $("#debugDiv").offset().top }, 1600);
            }
        });
    });

    // Function to handle role change in the Add New User form
    $('#user_role').on('change', function() {
        var selectedRole = $(this).val();
        var $programCodeField = $('#user_dept_prog_code');

        // Check if the selected role is DEPT_USER
        if (selectedRole === 'DEPT_USER') {
            // Enable the Program Code field
            $programCodeField.prop('disabled', false);
            $programCodeField.closest('.form-group').show();

            // // Initialize or refresh Select2
            // if ($programCodeField.hasClass("select2-hidden-accessible")) {
            //     $programCodeField.select2('destroy');
            // }

            // $programCodeField.select2({
            //     placeholder: "Select Program Codes",
            //     allowClear: true,
            //     width: '100%',
            //     dropdownParent: $('#modal_form_NewUser')
            // });
        } else {
            // Disable and clear the Program Code field
            $programCodeField.val(null).trigger('change');
            $programCodeField.prop('disabled', true);
            $programCodeField.closest('.form-group').hide();
        }
    });

    // Edit User button click
    $('table#tableUserList tbody').on('click', 'a.btnEditUser', function(event) {
        event.preventDefault();
        var $thisRow = $(this).closest('tr');
        if ($thisRow.hasClass('child')) $thisRow = $thisRow.prev();
        let formData = {
            cn:$thisRow.find("td").eq(1).text()
        }
        let role = $thisRow.find("td").eq(3).text()
        if(role==="Department User"){
            $.ajax({
                url: `/${systemContextRoot}/admin/visa/ajax/getProgrammesByCn?${csrfParameterName}=${csrfToken}`,
                type: "POST",
                data: JSON.stringify(formData),
                // processData: false,
                contentType: "application/json",
                success: function(result) {
                    console.log(`result: `,result)
                    openEditModal(event, result.displayProgrammesList, null, result.allSelected, result.selectedProgrammesList);
                },
                error: function(xhr, status, error) {
                    closeOverlayLoading();
                    let errorMessage = "Error checking cannot get programme code: " + error;
                    callback(errorMessage, true);
                }
            });
        }else{
            openEditModal(event, null, null, false, null);
        }
    });

    function openEditModal(event, displayProgList, userRole, allProg, selectedProgList){
        // Get the row data
        var $thisRow = $(event.target).closest('tr');
        if ($thisRow.hasClass('child')) $thisRow = $thisRow.prev();
            // // Get the role code from the edit button's data attribute
            var roleCode = $(event.target).closest('a.btnEditUser').attr('data-role');
            // Get the user's data from the row
            var userData = {
                fullName: $thisRow.find("td").eq(0).text(),
                user_staffNetworkID: $thisRow.find("td").eq(1).text(),
                department: $thisRow.find("td").eq(2).text(),
                role: $thisRow.find("td").eq(3).text(),
                startDate: $thisRow.find("td").eq(4).text() === '-' ? '' : $thisRow.find("td").eq(4).text(),
                endDate: $thisRow.find("td").eq(5).text() === '-' ? '' : $thisRow.find("td").eq(5).text(),
                status: $thisRow.find("td").eq(6).text().includes('Active') ? 'A' : 'I',
                networkId: $thisRow.find("td").eq(1).text(),
                roleCode: roleCode
            };
            console.log(`userData`,userData)
        
            // Set the form values
            var $form = $('#modal_form_EditUser form');
        
            // Set the role (display and hidden value)
            $form.find('input[name="user_role"]').val(userData.role);

            // Set the name (display and hidden value)
            $form.find('input[name="user_full_name"]').val(userData.fullName);
            $form.find('input[name="user_staffNetworkID"]').val(userData.user_staffNetworkID);
            var $programCodeRow = $form.find('div[id="edit_prog_code_row"]');
            if(roleCode === "DEPT_USER"){
                $programCodeRow.show();
                var $select = $form.find('select[id="editDeptProgCode"]');
                // Clear existing options
                $select.empty();
                $select.append('<option value="">Please Select</option>');
                // Add options for all available program codes
                if (displayProgList && displayProgList.length > 0) {
                    $.each(displayProgList, function(i, code) {
                        // If selectedProgList exists and contains this code, mark it as selected
                        var selected = (selectedProgList && selectedProgList.includes(code)) ? 'selected' : '';
                        $select.append(`<option value="${code}" ${selected}>${code}</option>`);
                    });
                    if(selectedProgList.length>0 && selectedProgList[0] === "ALL"){
                        $("#editAllProgCheckbox").prop('checked', true);
                        $("#editDeptProgCode").prop('disabled', true);
                    }
                }
                // Initialize Select2 for the dropdown
                // $select.select2({
                //     placeholder: "Select Program Codes",
                //     allowClear: true,
                //     width: '100%',
                //     dropdownParent: $('#modal_form_EditUser')
                // });
                // Set the "All Programs" checkbox
                $form.find('input[id="editAllProgCheckbox"]').prop('checked', allProg === true);
            }else{
                $programCodeRow.hide();
            }
            // Set the dates
            $form.find('input[name="user_periodStartDate"]').val(userData.startDate);
            $form.find('input[name="user_periodEndDate"]').val(userData.endDate);
        
            // Set the status
            $form.find(`input[name="user_status"][value="${userData.status}"]`).prop('checked', true);
            $form.find('input[name="networkId"]').val(userData.networkId);
            $form.find('input[name="roleCode"]').val(userData.roleCode);
            var statusVal = $thisRow.find("td").eq(6).text().substring(0, 1);
            statusVal == "A" ? $('#modal_form_EditUser input[name="user_status"][value="A"]').prop('checked', true) : $('#modal_form_EditUser input[name="user_status"][value="I"]').prop('checked', true);

            // //set prog code
            // $form.find(`input[name="editAllProg"]`).prop('checked', allProg);
            $('#modal_form_EditUser').modal('show');

            if ($('#modal_form_EditUser select[name="user_staffNetworkID"]').length) {
                initStaffSelect();
            }

    }

    // Edit User submission
    $('#modal_form_EditUser button#btnEdit').click(function(event) {
        event.preventDefault();
        // showOverlayLoading();

        let $form = $(this).closest('form');
        let formData = readEditUserForm($form);
        console.log(`formData: `,formData)

        if (!formData.periodStartDate || isNaN(formData.periodStartDate.valueOf())) {
            closeOverlayLoading();
            alert("Please select a valid start date");
            return;
        }
        $.ajax({
            type: "post",
            contentType: "application/json",
            url: `/${systemContextRoot}/admin/ajax/accessControl/userRole/save?${csrfParameterName}=${csrfToken}`,
            data: JSON.stringify(formData),
            dataType: "json",
            cache: false,
            timeout: 600000,
            success: function(data) {
                closeOverlayLoading();
                new Noty({
                    text: '<i class="icon-checkmark-circle icon-2x mr-2"></i> Saved Successfully!',
                    type: 'success',
                    layout: 'center',
                    modal: true
                }).show();
                window.location.reload();
            },
            error: function(e, textStatus, errorThrown) {
                var json = "<h4>Ajax Response</h4><pre>" + e.responseText + "</pre>";
                $('#debugDiv .card-body').html(json);
                console.log("ERROR : ", e);
                console.log("textStatus : ", textStatus);
                console.log("errorThrown : ", errorThrown);
                closeOverlayLoading();
                $('html, body').animate({ scrollTop: $("#debugDiv").offset().top }, 1600);
            }
        });
    });

    // Delete User button click
    $('table#tableUserList tbody').on('click', 'a.btnDeleteUser', function(event) {
        var $delRow = $(this).closest('tr');
        if ($delRow.hasClass('child')) $delRow = $delRow.prev();

        var delObj = {};
        delObj.cn = $delRow.find("td").eq(1).text();
        delObj.role = { code: $(this).attr('data-role') };

        var swalInit = swal.mixin({
            buttonsStyling: false,
            confirmButtonClass: 'btn btn-primary',
            cancelButtonClass: 'btn btn-light'
        });

        new swalInit({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            type: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'No, cancel!',
            confirmButtonClass: 'btn btn-success',
            cancelButtonClass: 'btn btn-danger',
            buttonsStyling: false
        }).then(function(result) {
            if (result.value) {
                $.ajax({
                    type: "post",
                    contentType: "application/json",
                    url: `/${systemContextRoot}/admin/ajax/accessControl/userRole/delete?${csrfParameterName}=${csrfToken}`,
                    data: JSON.stringify(delObj),
                    dataType: "json",
                    cache: false,
                    timeout: 600000,
                    success: function(data) {
                        new Noty({
                            text: '<i class="icon-cancel-circle2 icon-2x mr-2"></i> Deleted Successfully!',
                            type: 'success',
                            layout: 'center'
                        }).show();
                        table.ajax.reload(null, false);
                        closeOverlayLoading();
                        $('html, body').animate({ scrollTop: $("#debugDiv").offset().top }, 1600);
                    },
                    error: function(e, textStatus, errorThrown) {
                        var json = "<h4>Ajax Response</h4><pre>" + e.responseText + "</pre>";
                        $('#debugDiv .card-body').html(json);
                        console.log("ERROR : ", e);
                        console.log("textStatus : ", textStatus);
                        console.log("errorThrown : ", errorThrown);
                        closeOverlayLoading();
                        $('html, body').animate({ scrollTop: $("#debugDiv").offset().top }, 1600);
                    }
                });
            }
        });
    });

    // Add change event handler for staff selection
    $('#user_staffNetworkID').on('change', function() {
        const selectedStaffId = $(this).val();
        if (selectedStaffId) {
            showOverlayLoading();
            $.ajax({
                url: `/${systemContextRoot}/admin/ajax/accessControl/getProgrammesCodeByStaffNo?${csrfParameterName}=${csrfToken}`,
                type: "POST",
                data: JSON.stringify({ instituteId: $(this).find('option:selected').attr("data-institute-id") }),
                contentType: "application/json",
                success: function(programCodeList) {
                    if(programCodeList && programCodeList.length > 0){
                        $('#user_dept_prog_code').empty();
                        $('#user_dept_prog_code').append('<option value="">Please Select</option>');
                        // Add new options based on the returned program codes
                        $.each(programCodeList, function(i, code) {
                            $('#user_dept_prog_code').append(`<option value="${code}">${code}</option>`);
                        });
                        // Refresh the select2 control
                        $('#user_dept_prog_code').trigger('change');

                    }else{
                        $('#user_dept_prog_code').empty();
                        $('#allProgCheckbox').prop('disabled',true);
                    }
                    closeOverlayLoading();
                },
                error: function(xhr, status, error) {
                    // console.error("Error fetching program codes:", error);
                    closeOverlayLoading();
                    // Show error message to user
                    new Noty({
                        text: 'Failed to load program codes for the selected staff',
                        type: 'error'
                    }).show();
                }
            });
        } else {
            // // Clear program codes if no staff selected
            // $('#user_dept_prog_code').empty().append('<option value="">Please Select</option>');
            // $('#user_dept_prog_code').trigger('change');
        }
    });

    function readNewUserForm($form) {
        let formData = {};
        let selectedOption = $form.find('select[name="user_staffNetworkID"] option:selected');
        formData.cn = $form.find('select[name="user_staffNetworkID"]').val();
        formData.instituteId = selectedOption.attr("data-institute-id"); // Get instituteId from data attribute
        formData.role = { code: $form.find('select[name="user_role"]').val() };
        formData.periodStartDate = moment($form.find('input[name="user_periodStartDate"]').val(), 'YYYY-MM-DD');
        formData.periodEndDate = moment($form.find('input[name="user_periodEndDate"]').val(), 'YYYY-MM-DD');
        formData.status = "A";
        if($form.find('input[name="allProg"]').is(':checked')){
            formData.progCodeList = ["ALL"];
        }else{
            formData.progCodeList = $form.find('select[name="user_dept_prog_code"]').val() || [];
        }
        return formData;
    }

    function readEditUserForm($form) {
        let formData = {};
        formData.cn = $form.find('input[name="user_staffNetworkID"]').val();
        formData.role = { code: $form.find('input[name="roleCode"]').val() };
        formData.status = $form.find('input[name="user_status"]:checked').val();
        formData.periodStartDate = moment($form.find('input[name="user_periodStartDate"]').val(), 'YYYY-MM-DD');
        formData.periodEndDate = moment($form.find('input[name="user_periodEndDate"]').val(), 'YYYY-MM-DD');
        formData.status = $form.find('input[name="user_status"]:checked').val();;
        if($form.find('input[name="editAllProg"]').is(':checked')){
            formData.progCodeList = ["ALL"];
        }else{
            formData.progCodeList = $form.find('select[name="deptProgCode"]').val() || [];
        }
        return formData;
    }
});
</script>
</body>
</html>