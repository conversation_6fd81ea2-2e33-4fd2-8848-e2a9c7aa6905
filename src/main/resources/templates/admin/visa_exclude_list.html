<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/admin_head :: head"></head>
<body>
<!-- Main navbar -->
<div th:replace="fragments/admin_navbar :: navbar"></div>
<!-- /main navbar -->

<div id="data-container"
     th:attr="data-user-code=${userCode}"
></div>
<!-- Page header -->
<div class="page-header">
    <div class="page-header-content header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-compose mr-2"></i> <span class="font-weight-semibold">Visa Exclude List</span></h4>
            <a href="#" class="header-elements-toggle text-default d-md-none navbar-toggler sidebar-mobile-component-toggle"><i class="icon-more"></i></a>
        </div>
        <div class="header-elements d-none py-0 mb-3 mb-md-0">
            <div class="breadcrumb">
                <a th:href="@{'/'}" class="breadcrumb-item"><i class="icon-home2 mr-2"></i> Home</a>
                <span class="breadcrumb-item active">Visa Exclude List</span>
            </div>
        </div>
    </div>
</div>
<!-- /page header -->

<!-- Page content -->
<div class="page-content pt-0">
<!--    <link th:href="@{/assets/css/view_aap_list.css}" rel="stylesheet" type="text/css">-->
    <!-- Main content -->
    <div class="content-wrapper">
        <!-- Content area -->
        <div class="content">
            <!-- Search Criteria -->
            <div class="card">
                <div class="card-header header-elements-inline">
                    <h5 class="card-title"><i class="icon-filter4 mr-2"></i>Search Student Visa Records</h5>
                    <div class="header-elements">
                        <div class="list-icons"></div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="searchForm">
                        <div class="form-group row">
                            <label for="studentID" class="col-4 col-md-2 col-form-label">Student ID</label>
                            <div class="col-8 col-md-4">
                                <input type="text" class="form-control" name="studentID" id="studentID" placeholder="Enter Student ID">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">Programme </label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="programCode" id="select_programCode">
                                    <option value="">Please select</option>
                                    <th:block th:each="programme : ${programmes}">
                                        <option th:value="${programme}" th:text="${programme}"></option>
                                    </th:block>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">VISA Number</label>
                            <div class="col-8 col-md-4">
                                <input type="text" class="form-control" name="visaNumber" id="select_visaNumber" placeholder="Enter VISA Number">
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">VISA End Date</label>
                            <div class="col-8 col-md-8 d-flex">
                                <div class="mr-3">
                                    <label for="visaExpiryDateFr">From</label>
                                    <input type="date" class="form-control" id="visaExpiryDateFr" name="visaExpiryDateFr">
                                </div>
                                <div>
                                    <label for="visaExpiryDateTo">To</label>
                                    <input type="date" class="form-control" id="visaExpiryDateTo" name="visaExpiryDateTo">
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">VISA Type</label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="visaType" id="select_visaType">
                                    <option value="">Please select</option>
                                    <th:block th:each="entry : ${visaTypes}">
                                        <option th:value="${entry['VISATYPECODE']}" th:text="${entry['VISATYPEDESC']}"></option>
                                    </th:block>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">VISA Submission Status</label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="submissionStatus" id="select_submissionStatus">
                                    <option value="">Please select</option>
                                    <option value="UNASSESSED">Unassessed</option>
                                    <option value="DRAFT">Draft</option>
                                    <option value="Processing">Submitted</option>
                                    <!-- <option value="APPROVED">Approved</option> -->
                                    <th:block th:each="submissionStatus : ${submissionStatuses}">
                                        <option th:value="${submissionStatus.code}" th:text="${submissionStatus.description}"></option>
                                    </th:block>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-4 col-md-2 col-form-label">Excluded?</label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="excluded" id="select_excluded" required>
                                    <option value="ALL">All</option>
                                    <option value="Y">Yes</option>
                                    <option value="N">No</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-md-8 form-group text-center">
                                <button type="button" class="btn btn-primary" id="btnSearch">
                                    <i class="icon-search4 mr-2"></i> Search
                                </button>
                                <button type="button" class="btn btn-warning" id="btnReset">
                                    <i class="icon-reset mr-2"></i> Reset
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Selection -->
                    <div class="card-body">
                        <form id="selectionForm">
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <table id="table_selection" class="table table-sm table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th>Student ID</th>
                                            <th>Student Name</th>
                                            <th>Faculty</th>
                                            <th>Programme</th>
                                            <th>Prog Year</th>
                                            <th>Admit Term</th>
                                            <th>Year Status</th>
                                            <th>VISA Type</th>
                                            <th>VISA Number</th>
                                            <th>VISA End Date</th>
                                            <th>VISA Expiry Status</th>
                                            <th>Excluded?</th>
                                            <th>REG Remark</th>
                                            <th>REG Userstamp</th>
                                            <th>REG Timestamp</th>
                                            <th>GAO remark</th>
                                            <th>GAO Userstamp</th>
                                            <th>GAO Timestamp</th>
                                            <th>Programme remark</th>
                                            <th>Programme Userstamp</th>
                                            <th>Programme Timestamp</th>
                                        </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                    <div>
                                        <input type="hidden" name="selection_check">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<div th:replace="fragments/admin_page_footer"></div>
<!-- /footer -->

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmSaveModal" tabindex="-1" role="dialog" aria-labelledby="confirmSaveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmSaveModalLabel">Confirm Save</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to save these changes?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSave">Yes, Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    const container = document.getElementById('data-container');
    const userCode = container.dataset.userCode
    $(document).ready(function() {
        var table;
        var originalData = {};
        var pageStateKey = 'visa_exclude_list_state';

        // State management functions
        function savePageState() {
            if (!table) return;

            // Get current DataTable state
            const dtState = table.state();
            const currentPage = table.page.info();

            const state = {
                // Search form data
                searchCriteria: readSearchCriteriaForm(),
                // DataTable data and state
                tableData: table.data().toArray(),
                tableState: {
                    // Core DataTable state
                    start: dtState ? dtState.start : currentPage.start,
                    length: dtState ? dtState.length : currentPage.length,
                    order: dtState ? dtState.order : table.order(),
                    search: dtState ? dtState.search : { search: table.search() },
                    // Additional page info
                    page: currentPage.page,
                    pages: currentPage.pages,
                    recordsTotal: currentPage.recordsTotal,
                    recordsDisplay: currentPage.recordsDisplay
                },
                // Additional UI state
                timestamp: Date.now()
            };

            // Save to session storage for current session
            try {
                sessionStorage.setItem(pageStateKey, JSON.stringify(state));
                console.log('Page state saved successfully:', {
                    search: state.tableState.search.search,
                    page: state.tableState.page,
                    length: state.tableState.length,
                    recordsTotal: state.tableState.recordsTotal
                });
            } catch (e) {
                console.warn('Failed to save page state:', e);
            }
        }

        function restorePageState() {
            try {
                const savedState = sessionStorage.getItem(pageStateKey);
                if (!savedState) return false;

                const state = JSON.parse(savedState);

                // Check if state is not too old (optional - 1 hour limit)
                if (Date.now() - state.timestamp > 3600000) {
                    sessionStorage.removeItem(pageStateKey);
                    return false;
                }

                // Restore search form
                restoreSearchForm(state.searchCriteria);

                // Restore table data and state
                if (state.tableData && state.tableData.length > 0) {
                    console.log(`Restoring ${state.tableData.length} records from session storage`);

                    // Store original data for comparison
                    originalData = {};
                    state.tableData.forEach(row => {
                        originalData[row.studentId] = {
                            excludeStatus: row.excludeStatus || 'N',
                            exclusionRemark: row.exclusionRemark !== null && row.exclusionRemark !== undefined ? row.exclusionRemark : '',
                            gaoRemark: row.gaoRemark !== null && row.gaoRemark !== undefined ? row.gaoRemark : '',
                            programmeRemark: row.programmeRemark !== null && row.programmeRemark !== undefined ? row.programmeRemark : ''
                        };
                    });

                    initSelection();
                    table.clear().rows.add(state.tableData).draw(false);

                    // Restore table state (pagination, sorting, search, etc.)
                    if (state.tableState) {
                        setTimeout(function() {
                            console.log('Starting DataTable state restoration...');

                            // Step 1: Restore search first (affects filtering)
                            if (state.tableState.search && state.tableState.search.search) {
                                console.log('Restoring search:', state.tableState.search.search);
                                table.search(state.tableState.search.search);
                            }

                            // Step 2: Restore page length (affects pagination)
                            if (state.tableState.length !== undefined) {
                                console.log('Restoring page length:', state.tableState.length);
                                table.page.len(state.tableState.length);
                            }

                            // Step 3: Restore sorting
                            if (state.tableState.order && state.tableState.order.length > 0) {
                                console.log('Restoring order:', state.tableState.order);
                                table.order(state.tableState.order);
                            }

                            // Step 4: Apply changes and then restore page
                            table.draw(false);

                            // Step 5: Restore current page after draw is complete
                            setTimeout(function() {
                                if (state.tableState.page !== undefined && state.tableState.page >= 0) {
                                    console.log('Restoring page:', state.tableState.page);
                                    table.page(state.tableState.page).draw(false);
                                }

                                console.log('DataTable state restoration complete');
                            }, 100);

                        }, 100);
                    }

                    console.log('Page state restored successfully');
                    return true;
                }
            } catch (e) {
                console.warn('Failed to restore page state:', e);
                sessionStorage.removeItem(pageStateKey);
            }
            return false;
        }

        function restoreSearchForm(searchCriteria) {
            if (!searchCriteria) return;

            const $form = $('form#searchForm');
            $form.find('input#studentID').val(searchCriteria.studentId || '');
            $form.find('select#select_programCode').val(searchCriteria.programCode || '');
            $form.find('input#select_visaNumber').val(searchCriteria.visaNumber || '');
            $form.find('input#visaExpiryDateFr').val(searchCriteria.visaExpiryDateFr || '');
            $form.find('input#visaExpiryDateTo').val(searchCriteria.visaExpiryDateTo || '');
            $form.find('select#select_visaType').val(searchCriteria.visaType || '');
            $form.find('select#select_submissionStatus').val(searchCriteria.submissionStatus || '');
            $form.find('select#select_excluded').val(searchCriteria.excluded || 'ALL');
        }

        function clearPageState() {
            try {
                sessionStorage.removeItem(pageStateKey);
                console.log('Page state cleared');
            } catch (e) {
                console.warn('Failed to clear page state:', e);
            }
        }

        // Update session storage with current form data after save
        function updateSessionStorageWithCurrentData(updatedData) {
            try {
                const savedState = sessionStorage.getItem(pageStateKey);
                if (!savedState) return;

                const state = JSON.parse(savedState);
                if (!state.tableData || state.tableData.length === 0) return;

                console.log('Updating session storage with saved changes...');

                // Update the table data with the saved changes
                updatedData.forEach(update => {
                    const studentIndex = state.tableData.findIndex(item => item.studentPidm === update.studentPidm);
                    if (studentIndex !== -1) {
                        // Update the record with saved values
                        state.tableData[studentIndex].excludeStatus = update.isExcluded === 'Y' ? 'E' : 'N';
                        state.tableData[studentIndex].exclusionRemark = update.exclusionRemark;
                        state.tableData[studentIndex].gaoRemark = update.gaoRemark;
                        state.tableData[studentIndex].programmeRemark = update.programmeRemark;

                        console.log(`Updated student ${state.tableData[studentIndex].studentId} in session storage`);
                    }
                });

                // Update timestamp
                state.timestamp = Date.now();

                // Save back to session storage
                sessionStorage.setItem(pageStateKey, JSON.stringify(state));
                console.log('Session storage updated successfully with saved changes');

            } catch (e) {
                console.error('Failed to update session storage with current data:', e);
            }
        }

        function initSelection() {
            if ($.fn.DataTable.isDataTable('#table_selection')) {
                $('#table_selection').off('input', '.remark-input');
                $('#table_selection').off('change', '.upload-checkbox');
                $('#table_selection').DataTable().clear().destroy();
            }
            table = $('#table_selection').DataTable({
                responsive: false,
                scrollX: true,
                paging: true,
                pageLength: 25,
                lengthMenu: [5, 10, 25, 50, 100],
                autoWidth: false,
                dom: 'Bfrtip',
                buttons: ['print'],
                // Enable state saving for DataTable's built-in features
                stateSave: true,
                stateDuration: 3600, // 1 hour in seconds
                stateLoadCallback: function(settings) {
                    // Don't use DataTable's default state loading, we handle it ourselves
                    return null;
                },
                stateSaveCallback: function(settings, data) {
                    // Don't use DataTable's default state saving, we handle it ourselves
                    return;
                },
                columns: [
                    {
                        data: 'studentPidm',
                        visible: false
                    },
                    { data: 'studentId' },
                    { data: 'studentName' },
                    { data: 'faculty' },
                    { data: 'programme' },
                    { data: 'programmeYear' },
                    { data: 'admitTerm' },
                    { data: 'orgVisaDesc' },
                    { data: 'orgVisaNumber' },
                    { data: 'orgVisaExpiryDate' },
                    { data: 'expiryStatus' },
                    {
                        data: 'excludeStatus',
                        render: function(data, type, row) {
                            return `<input type="checkbox" class="upload-checkbox" ${data === 'E' ? 'checked' : ''}>`;
                        }
                    },
                    {
                        data: 'exclusionRemark',
                        render: function(data, type, row) {
                            if(userCode === 'REG_USER'){
                                return `<input type="text" class="form-control remark-input"  value="${data !== null && data !== undefined ? data : ''}">`;
                            }else{
                                return `<input type="text" class="form-control remark-input" disabled value="${data !== null && data !== undefined ? data : ''}">`;
                            }
                        }
                    },
                    { data: 'excludeRemarksUserstamp' },
                    { data: 'excludeRemarksTimestamp' },
                    {
                        data: 'gaoRemark',
                        render: function(data, type, row) {
                            if(userCode === 'GAO_USER'){
                                return `<input type="text" class="form-control gao-remark-input" value="${data !== null && data !== undefined ? data : ''}">`;
                            }else{
                                return `<input type="text" class="form-control gao-remark-input" disabled value="${data !== null && data !== undefined ? data : ''}">`;
                            }
                        }
                    },
                    { data: 'gaoRemarkUserstamp' },
                    { data: 'gaoRemarkTimestamp' },
                    {
                        data: 'programmeRemark',
                        render: function(data, type, row) {
                            if(userCode === 'DEPT_USER'){
                                return `<input type="text" class="form-control programme-remark-input" value="${data !== null && data !== undefined ? data : ''}">`;
                            }else{
                                return `<input type="text" class="form-control programme-remark-input" disabled value="${data !== null && data !== undefined ? data : ''}">`;
                            }
                        }
                    },
                    { data: 'programmeRemarkUserstamp' },
                    { data: 'programmeRemarkTimestamp' },
                    {
                        data: 'gorvisaSeqNo',  // Add gorvisaSeqNo as a hidden column
                        visible: false         // Hide it from the table display
                    }
                ]
            });

            // Save state when table state changes
            table.on('page.dt length.dt order.dt search.dt draw.dt', function(e, settings) {
                // Only save state for user-initiated changes, not programmatic ones
                if (settings.oFeatures.bServerSide === false) {
                    setTimeout(function() {
                        savePageState();
                        console.log('Table state changed, saved state');
                    }, 150); // Increased delay to ensure all changes are applied
                }
            });

            // Additional event handlers for more comprehensive state tracking
            table.on('column-visibility.dt', function() {
                setTimeout(savePageState, 100);
            });
        }

        // Add a save button below the table
        $('<button type="button" class="btn btn-success" id="btnSave">Save Changes</button>').insertAfter('#table_selection');

        $('#btnSave').click(function() {
            // Open confirmation modal
            $('#confirmSaveModal').modal('show');
        });

        $('#confirmSave').click(function() {
            let updatedData = [];
            let hasChanges = false;
            table.rows().every(function() {
                let data = this.data();
                let checkbox = $(this.node()).find('.upload-checkbox');
                let isExcluded = checkbox.is(':checked');
                let originalExcluded = originalData[data.studentId].excludeStatus === 'E';
                let currentRemark = $(this.node()).find('.remark-input').val() || '';
                let gaoCurrentRemarkInput = $(this.node()).find('.gao-remark-input').val() || '';
                let programmeCurrentRemarkInput = $(this.node()).find('.programme-remark-input').val() || '';
                let originalRemark = originalData[data.studentId].exclusionRemark || '';
                let originalGaoRemark = originalData[data.studentId].gaoRemark || '';
                let originalProgrammeRemark = originalData[data.studentId].programmeRemark || '';
                //data checking
                if (isExcluded !== originalExcluded || currentRemark !== originalRemark || gaoCurrentRemarkInput !== originalGaoRemark || programmeCurrentRemarkInput !== originalProgrammeRemark) {
                    hasChanges = true;
                    updatedData.push({
                        studentPidm: data.studentPidm,
                        // studentId: data.studentId,
                        visaType: data.orgVisaType,
                        seqNo: data.gorvisaSeqNo,
                        isExcluded: isExcluded ? 'Y' : 'N',
                        exclusionRemark: currentRemark,
                        gaoRemark:gaoCurrentRemarkInput,
                        programmeRemark:programmeCurrentRemarkInput
                    });
                }
            });

            if (!hasChanges) {
                alert("No changes detected.");
                $('#confirmSaveModal').modal('hide');
                return;
            }

            const formData = readSearchCriteriaForm();
            formData.update = updatedData;
            showOverlayLoading();
            $.ajax({
                type: "POST",
                url: "/[[${systemContextRoot}]]/admin/visa/ajax/saveVisaExclude?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                data: JSON.stringify(formData),
                contentType: "application/json",
                success: function(response) {
                    $('#confirmSaveModal').modal('hide');
                    closeOverlayLoading();

                    // Update session storage with current changes before re-searching
                    updateSessionStorageWithCurrentData(updatedData);

                    searchMappingResult();
                },
                error: function() {
                    alert("Error saving changes: " + (xhr.responseText || error));
                    $('#confirmSaveModal').modal('hide');
                    closeOverlayLoading();
                }
            });
        });

        // Initialize the page
        function initPage() {
            // Try to restore previous state first
            if (!restorePageState()) {
                // If no state to restore, initialize empty table
                initSelection();
            }
        }

        // Make state management functions available globally for testing
        window.savePageState = savePageState;
        window.restorePageState = restorePageState;
        window.clearPageState = clearPageState;

        // Debug function to check session storage and table state
        window.debugVisaExcludeListState = function() {
            const savedState = sessionStorage.getItem(pageStateKey);
            const tableData = table ? table.data().toArray() : [];

            console.log('=== Visa Exclude List State Debug ===');
            console.log('Session storage state:', savedState ? JSON.parse(savedState) : 'Not found');
            console.log('Current table data count:', tableData.length);
            console.log('Table initialized:', !!table);

            if (tableData.length > 0) {
                console.log('Sample table record:', tableData[0]);
            }

            return {
                sessionState: savedState ? JSON.parse(savedState) : null,
                tableDataCount: tableData.length,
                tableInitialized: !!table,
                sampleRecord: tableData[0] || null
            };
        };

        // Handle browser back button
        window.addEventListener('pageshow', function(event) {
            // This event fires when page is loaded from cache (back button)
            if (event.persisted) {
                console.log('Page loaded from cache, attempting to restore state');
                restorePageState();
            }
        });

        // Save state when user navigates away (including browser back button)
        window.addEventListener('beforeunload', function() {
            savePageState();
        });

        // Handle browser back/forward navigation
        window.addEventListener('popstate', function(event) {
            console.log('Popstate event detected, attempting to restore state');
            setTimeout(function() {
                restorePageState();
            }, 100);
        });

        $('#btnReset').click(function() {
            $('#searchForm')[0].reset();
            clearPageState(); // Clear saved state when resetting
        });

        $('#btnSearch').click(function(event) {
            event.preventDefault();
            searchMappingResult();
        });

        function searchMappingResult() {
            const validateResults = $('form#searchForm').valid();
            if (!validateResults) {
                return false;
            }
            const searchCriteria = readSearchCriteriaForm();
            showOverlayLoading();
            $.ajax({
                type: "POST",
                contentType: "application/json",
                url: "/[[${systemContextRoot}]]/admin/visa/ajax/search?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                data: JSON.stringify(searchCriteria),
                cache: false,
                timeout: 180000,
                success: function(data) {
                    originalData = {};
                    data.forEach(row => {
                        originalData[row.studentId] = {
                            excludeStatus: row.excludeStatus || 'N',
                            exclusionRemark: row.exclusionRemark !== null && row.exclusionRemark !== undefined ? row.exclusionRemark : ''
                        };
                    });

                    initSelection();
                    table.clear().rows.add(data).draw();

                    // Save state after successful search
                    setTimeout(savePageState, 100); // Small delay to ensure table is fully rendered

                    closeOverlayLoading();
                    if ($('#btnSave').length === 0) { // Check if the button already exists
                        $('<button type="button" class="btn btn-success" id="btnSave">Save Changes</button>').insertAfter('#table_selection');
                    }
                },
                error: function(e, textStatus, err) {
                    closeOverlayLoading();
                    if (textStatus == 'timeout') {
                        showNotySimple('warning', 'There are too many results for this search. Please modify your search.', {timeout: 30000});
                    } else if (e.status == '422') {
                        showNotySimple('error', e.responseText);
                    } else if (e.status == '405') {
                        // sessTimeOut();
                    } else {
                        showNotySimple('error', 'Your action cannot be processed.<br>Please refresh.<br>If the problem persists, please contact system support.', {timeout: false});
                    }
                }
            });
        }

        function readSearchCriteriaForm() {
            const $form = $('form#searchForm');
            return {
                studentId: $form.find('input#studentID').val(),
                programCode: $form.find('select#select_programCode option:selected').val(),
                visaNumber: $form.find('input#select_visaNumber').val(),
                visaExpiryDateFr: $form.find('input#visaExpiryDateFr').val(),
                visaExpiryDateTo: $form.find('input#visaExpiryDateTo').val(),
                visaType: $form.find('select#select_visaType option:selected').val(),
                submissionStatus: $form.find('select#select_submissionStatus option:selected').val(),
                excluded: $form.find('select#select_excluded').val()
            };
        }

        initPage();
    });
</script>
</body>
</html>