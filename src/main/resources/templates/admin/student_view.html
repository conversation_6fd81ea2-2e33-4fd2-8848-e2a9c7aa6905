<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/admin_head :: head">
</head>
<style>
    .preview-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 15px;
    }

    .image-preview {
        position: relative;
        margin: 5px;
        display: inline-block;
    }

    .preview-img, .pdf-preview canvas {
        max-width: 100px;
        border-radius: 5px;
        border: 1px solid #ddd;
    }

    /*.close-btn {*/
    /*    position: absolute;*/
    /*    top: 5px;*/
    /*    right: 5px;*/
    /*    background: red;*/
    /*    color: white;*/
    /*    font-size: 14px;*/
    /*    width: 20px;*/
    /*    height: 20px;*/
    /*    text-align: center;*/
    /*    line-height: 18px;*/
    /*    border-radius: 50%;*/
    /*    cursor: pointer;*/
    /*}*/

    .pdf-preview {
        width: 100px;
        height: auto;
        border: 1px solid #ddd;
        border-radius: 5px;
        text-align: center;
        font-weight: bold;
        background: #f8f8f8;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        text-align: center;
    }

    .modal-content {
        max-width: 90%;
        max-height: 90%;
        display: block;
        margin: auto;
        object-fit: contain;
        margin-top: 3%;
    }

    .modal-close {
        position: absolute;
        top: 10px;
        right: 20px;
        font-size: 30px;
        color: white;
        cursor: pointer;
    }

    .modal-close:hover {
        color: red;
    }

    /* Added for visa info alignment */
    .visa-info-item {
        margin-bottom: 10px;
    }

    /* Error message styling */
    #errorMessage {
        display: none;
        margin-top: 10px;
    }

    /* Custom Modal Styles */
    .custom-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 99999;
    }

    .custom-modal-content {
        background-color: #fff;
        margin: 15% auto;
        padding: 20px;
        border: 1px solid #888;
        width: 400px;
        border-radius: 5px;
        position: relative;
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            transform: translateY(-100px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .custom-modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
    }

    .custom-modal-body {
        padding: 20px 0;
    }

    .custom-modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        border-top: 1px solid #ddd;
        padding-top: 15px;
    }

    .custom-close {
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .custom-close:hover {
        color: #666;
    }

    .custom-btn {
        padding: 8px 20px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    .custom-btn-primary {
        background-color: #007bff;
        color: white;
    }

    .custom-btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .attachment-controls {
        margin-top: 15px;
    }

    .add-file-btn {
        cursor: pointer;
        border: 2px dashed #ddd;
        padding: 15px;
        text-align: center;
        border-radius: 5px;
        background-color: #f8f9fa;
        transition: all 0.3s ease;
        max-width: 300px;
    }

    .add-file-btn:hover {
        border-color: #007bff;
        background-color: #e9f0ff;
    }

    .plus-icon {
        font-size: 24px;
        color: #6c757d;
        margin-bottom: 5px;
    }

    .add-file-btn span {
        color: #6c757d;
        font-size: 14px;
    }

    .font-icon-cust {
        cursor: pointer;
    }

</style>
<body>
<div id="data-container" th:attr="data-user-code=${userCode},
data-excluded=${studentDetails.excluded}, data-org-visa-type=${studentDetails.orgVisaType}, data-org-visa-seq-no=${studentDetails.orgVisaSeqNo}, data-upload-seq-no=${visaUploadInfo?.seqNo}"></div>
<!-- Main navbar -->
<div th:replace="fragments/admin_navbar :: navbar"></div>
<!-- /main navbar -->

<!-- Page content -->
<div class="page-content pt-0">
    <!-- Main content -->
    <div id="excludeStatus" th:value="${studentDetails.excluded}" style="display: none;"></div>
    <div class="content-wrapper">
        <!-- Content area -->
        <div class="content">
            <!-- Visa Upload Details -->
            <div class="card">
                <div class="card-header header-elements-inline">
                    <h5 class="card-title"><i class="icon-user mr-2"></i> Visa Upload Details</h5>
                </div>
                <div class="card-body">
                    <div class="student-details-container">
                        <div class="row">
                            <div class="col-8">
                                <form id="studentDetailsForm">
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Student ID</label>
                                        <div id="studentId" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.studentId}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Student Name</label>
                                        <div id="studentName" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.studentName}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Personal Email</label>
                                       <div id="studentEmail" class="col-8 col-md-4 form-control-plaintext"
                                            th:text="${studentDetails.studentEmail}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Phone Number</label>
                                       <div id="studentPhoneNumber" class="col-8 col-md-4 form-control-plaintext"
                                            th:text="${studentDetails.studentPhoneNumber}"></div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Faculty</label>
                                        <div id="faculty" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.faculty}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Programme</label>
                                        <div id="programme" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.programme}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Programme Year</label>
                                        <div id="progYear" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.programmeYear}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Admit Term</label>
                                        <div id="admitTerm" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.admitTerm}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Original Visa Type</label>
                                        <div id="orgVisaType" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.orgVisaDesc}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Original Visa Number</label>
                                        <div id="orgVisaNumber" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.orgVisaNumber}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Original VISA End Date</label>
                                        <div id="orgVisaExpiryDate" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.orgVisaExpiryDate}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Expiry Status</label>
                                        <div id="visaExpiryStatus" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.visaExpiryStatus}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Uploaded Student name</label>
                                        <div id="visaStudentName" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.visaStudentName}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Uploaded Institution Name</label>
                                        <div id="visaInstitutionName" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.visaInstitutionName}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Type</label>
                                        <div class="col-8 col-md-4">
                                            <select th:if="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}" id="visaType" class="form-control">
                                                <th:block th:each="entry : ${visaTypes}">
                                                    <option th:value="${entry['VISATYPECODE']}"
                                                            th:text="${entry['VISATYPEDESC']}"
                                                            th:selected="${studentDetails.visaType == entry['VISATYPECODE']}">
                                                    </option>
                                                </th:block>
                                            </select>
                                            <div th:unless="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}" id="visaType" class="form-control-plaintext"
                                                 th:value="${visaUploadInfo?.vtypeCode}" th:text="${visaUploadInfo?.vtypeDesc}"></div>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Number</label>
                                        <div class="col-8 col-md-4">
                                            <input th:if="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}" id="visaNumber" type="text" class="form-control"
                                                   th:value="${studentDetails.visaNumber}">
                                            <div th:unless="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}" id="visaNumber" class="form-control-plaintext"
                                                 th:value="${studentDetails.visaNumber}" th:text="${studentDetails.visaNumber}"></div>
                                        </div>
                                    </div>


                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA End Date</label>
                                        <th:block th:if="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}">
                                            <div class="col-8 col-md-4">
                                                <input id="visaExpiryDate" type="date" class="form-control"
                                                       th:value="${studentDetails.visaExpiryDate}">
                                            </div>
                                        </th:block>
                                        <th:block th:unless="${userCode =='REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}">
                                            <div class="col-8 col-md-4">
                                                <div id="visaExpiryDate" class="form-control-plaintext"
                                                     th:text="${studentDetails.visaExpiryDate}"></div>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Submission Status</label>
                                        <div id="submissionStatus" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.submissionStatus}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Reminder 1 Sent Date</label>
                                        <div id="reminder1SentDate" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.reminder1SentDate}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">VISA Reminder 2 Sent Date</label>
                                        <div id="reminder2SentDate" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.reminder2SentDate}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Excluded</label>
                                        <div class="col-8 col-md-4">
                                            <input type="checkbox" id="excluded"
                                                   th:checked="${studentDetails.excluded == 'E'}">
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Excluded Reason</label>
                                        <div class="col-8 col-md-4">
                                            <div class="row">
                                                <div class="col-11">
                                                        <textarea style="resize: vertical; min-height: 100px;" id="exclusionRemark" class="form-control" maxlength="1000" multiple
                                                                  th:value="${studentDetails.exclusionRemark}" th:text="${studentDetails.exclusionRemark}"
                                                        ></textarea>
                                                </div>
                                                <div class="col-1">
                                                   <i class="icon-enlarge7 font-icon-cust" id="exclusionRemarkPopupButton"></i>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Excluded Reason Userstamp</label>
                                        <div id="exclusionRemarkUserstamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.exclusionRemarkUserstamp}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Excluded Reason Timestamp</label>
                                        <div id="exclusionRemarkTimestamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails?.exclusionRemarkTimestamp != null ? #temporals.format(studentDetails?.exclusionRemarkTimestamp, T(com.eduhk.sa.util.DateUtils).getDisplayDateTimeFormat()) : ''}"></div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">REG Remark</label>
                                        <th:block th:if="${userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div class="row">
                                                    <div class="col-11">
                                                        <textarea style="resize: vertical; min-height: 100px;" id="regRemark" class="form-control" maxlength="1000" multiple
                                                                  th:value="${studentDetails.regRemark}" th:text="${studentDetails.regRemark}"
                                                        ></textarea>
                                                    </div>
                                                    <div class="col-1">
                                                        <i class="icon-enlarge7 font-icon-cust" id="regRemarkPopupButton"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </th:block>
                                        <th:block th:unless="${userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div id="regRemark" class="form-control-plaintext"
                                                     th:text="${studentDetails.regRemark}"></div>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">REG Remark Userstamp</label>
                                        <div id="regRemarkUserstamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.regRemarkUserstamp}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">REG Remark Timestamp</label>
                                        <div id="regRemarkTimestamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails?.regRemarkTimestamp != null ? #temporals.format(studentDetails?.regRemarkTimestamp, T(com.eduhk.sa.util.DateUtils).getDisplayDateTimeFormat()) : ''}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">GAO Remark</label>
                                        <th:block th:if="${userCode =='GAO_USER' || userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div class="row">
                                                    <div class="col-11">
                                                        <textarea style="resize: vertical; min-height: 100px;" id="gaoRemark" class="form-control" maxlength="1000" multiple
                                                            th:value="${studentDetails.gaoRemark}" th:text="${studentDetails.gaoRemark}"
                                                        ></textarea>
                                                    </div>
                                                    <div class="col-1">
                                                        <i class="icon-enlarge7 font-icon-cust" id="gaoRemarkPopupButton"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </th:block>
                                        <th:block th:unless="${userCode =='GAO_USER' || userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div id="gaoRemark" class="form-control-plaintext"
                                                     th:text="${studentDetails.gaoRemark}"></div>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">GAO Remark Userstamp</label>
                                        <div id="gaoRemarkUserstamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.gaoRemarkUserstamp}"></div>

                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">GAO Remark Timestamp</label>
                                        <div id="gaoRemarkTimestamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails?.gaoRemarkTimestamp != null ? #temporals.format(studentDetails?.gaoRemarkTimestamp, T(com.eduhk.sa.util.DateUtils).getDisplayDateTimeFormat()) : ''}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Programme Remark</label>
                                        <th:block th:if="${userCode =='DEPT_USER' || userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div class="row">
                                                    <div class="col-11">
                                                        <textarea style="resize: vertical; min-height: 100px;" id="programmeRemark" class="form-control" maxlength="1000" multiple
                                                            th:value="${studentDetails.programmeRemark}" th:text="${studentDetails.programmeRemark}"
                                                        ></textarea>
                                                    </div>
                                                    <div class="col-1">
                                                        <i class="icon-enlarge7 font-icon-cust" id="programmeRemarkPopupButton"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </th:block>
                                        <th:block th:unless="${userCode =='DEPT_USER' || userCode =='REG_USER'}">
                                            <div class="col-8 col-md-4">
                                                <div id="programmeRemark" class="form-control-plaintext"
                                                     th:text="${studentDetails.programmeRemark}"></div>
                                            </div>
                                        </th:block>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Programme Remark Userstamp</label>
                                        <div id="programmeRemarkUserstamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails.programmeRemarkUserstamp}"></div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-4 col-md-2 col-form-label">Programme Remark Timestamp</label>
                                        <div id="programmeRemarkTimestamp" class="col-8 col-md-4 form-control-plaintext"
                                             th:text="${studentDetails?.programmeRemarkTimestamp != null ? #temporals.format(studentDetails?.programmeRemarkTimestamp, T(com.eduhk.sa.util.DateUtils).getDisplayDateTimeFormat()) : ''}"></div>
                                    </div>
                                </form>
                            </div>
                            <div class="col-4">
                                <!--                <img th:src="@{/path/to/visa-image.jpg}" alt="VISA Image" class="img-fluid">-->
                                <div class="form-group">
                                    <div class="preview-container" id="previewContainer">
                                        <th:block th:each="file : ${visaUploadInfo?.uploadedFiles}">
                                            <div class="image-preview"
                                                 th:attr="data-filename=${file.filename}, data-fileSeqNo=${file.fileSeqNo}, data-base64='data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}">
<!--                                                <span class="close-btn">×</span>-->
                                                <th:block th:if="${file.mimetype.startsWith('image/')}">
                                                    <img th:src="'data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}"
                                                         class="preview-img" alt="Uploaded Image"/>
                                                </th:block>
                                                <th:block th:if="${file.mimetype == 'application/pdf'}">
                                                    <!-- Canvas will be added by JS -->
                                                </th:block>
                                            </div>
                                        </th:block>

                                    </div>
<!--                                    <div class="attachment-controls">-->
<!--                                        <label for="additionalFiles" class="add-file-btn">-->
<!--                                            <div class="plus-icon">+</div>-->
<!--                                            <span>Attach Additional Files</span>-->
<!--                                        </label>-->
<!--                                        <input type="file" id="additionalFiles" multiple style="display: none;">-->
<!--                                    </div>-->
                                </div>
                            </div>
                            <div class="row col-md-4">
                            </div>
                            <div class="row col-md-4">
                                <div class="form-group text-center">
                                    <div class="">
                                        <button id="backButton" type="button" class="btn btn-secondary">Back to Student
                                            List
                                        </button>
                                        <button id="saveButton" type="button" class="btn btn-primary">Save</button>
                                        <th:block th:if="${userCode == 'REG_USER' and studentDetails.submissionStatus == 'SUBMITTED'}">
                                            <button id="rejectButton" type="button" class="btn btn-danger">Reject</button>
                                            <button id="approveButton" type="button" class="btn btn-success">Approve</button>
                                        </th:block>
                                    </div>
                                </div>
                            </div>
                            <div class="row col-md-4">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- /Visa Upload Details -->
            <!-- Visa History -->
            <div class="card mt-4" th:if="${!studentDetailsHistory.isEmpty()}">
                <div class="card-header header-elements-inline">
                    <h5 class="card-title"><i class="icon-history mr-2"></i> Visa History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                            <tr>
                                <!-- <th>Student ID</th>
                                <th>Student Name</th>
                                <th>Programme</th>
                                <th>Programme Year</th>
                                <th>Admit Term</th> -->
                                <th>Visa Type</th>
                                <th>Visa Number</th>
                                <th>VISA End Date</th>
                                <th>Visa Approval Date</th>
                                <th></th>
                                <!-- <th>Faculty</th>
                                <th>Reminder 1 Sent Date</th>
                                <th>Reminder 2 Sent Date</th> -->
                            </tr>
                            </thead>
                            <tbody>
                            <tr th:each="history : ${studentDetailsHistory}" class="visa-history-row"
                                th:attr="data-student-id=${studentDetails.studentId}, data-seq-no=${history.seqNo}">
                                <td th:text="${history.vtypeCode}"></td>
                                <td th:text="${history.visaNumber}"></td>
                                <td th:text="${#dates.format(history.visaExpiryDate, 'yyyy-MM-dd')}"></td>
                                <td th:text="${#dates.format(history.approvalDate, 'yyyy-MM-dd')}"></td>
                                <td>
                                    <button type="button" class="btn btn-primary view-btn">View</button>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /content area -->
    </div>
    <!-- /main content -->
</div>
<!-- /page content -->

<div id="customModal" class="custom-modal">
    <div class="custom-modal-content">
        <div class="custom-modal-header">
            <h3>Confirm Action</h3>
            <span class="custom-close">&times;</span>
        </div>
        <div class="custom-modal-body">
            <p>Are you sure you want to proceed?</p>
        </div>
        <div class="custom-modal-footer">
            <button id="customCancelBtn" class="custom-btn custom-btn-secondary">Cancel</button>
            <button id="customConfirmBtn" class="custom-btn custom-btn-primary">Confirm</button>
        </div>
    </div>
</div>
<div id="imageModal" class="modal">
    <span id="closeImageModal" class="modal-close">×</span>
    <img id="modalImage" class="modal-content">
</div>
<div id="pdfModal" class="modal">
    <span id="closePdfModal" class="modal-close">×</span>
    <iframe id="pdfFrame" class="modal-content" style="width: 80%; height: 80%;"></iframe>
</div>
<div id="remarkModal" class="modal">
    <div class="custom-modal-content">
        <div class="custom-modal-body">
<!--            <select class="form-control" name="remarkSelect" id="selected_remark">-->
                <select class="form-control" name="remarkSelect" id="exclusion_selected_remark">
                <option value="">Please select</option>
                <th:block th:each="item : ${exclusionRemarkLists}">
                    <option th:value="${item.text}" th:name="${item.code}" th:text="${item.text}" th:data-code="${item.code}"></option>
                </th:block>
            </select>
            <select class="form-control" name="remarkSelect" id="reg_selected_remark">
                <option value="">Please select</option>
                <th:block th:each="item : ${regRemarkLists}">
                    <option th:value="${item.text}" th:name="${item.code}" th:text="${item.text}" th:data-code="${item.code}"></option>
                </th:block>
            </select>
            <select class="form-control" name="remarkSelect" id="gao_selected_remark">
                <option value="">Please select</option>
                <th:block th:each="item : ${gaoRemarkLists}">
                    <option th:value="${item.text}" th:name="${item.code}" th:text="${item.text}" th:data-code="${item.code}"></option>
                </th:block>
            </select>
            <select class="form-control" name="remarkSelect" id="dept_selected_remark">
                <option value="">Please select</option>
                <th:block th:each="item : ${deptRemarkLists}">
                    <option th:value="${item.text}" th:name="${item.code}" th:text="${item.text}" th:data-code="${item.code}"></option>
                </th:block>
            </select>
    </option>
        </div>
        <div class="custom-modal-footer">
            <button id="remarkCancelBtn" class="custom-btn custom-btn-secondary">Cancel</button>
            <button id="remarkSelectBtn" class="custom-btn custom-btn-primary">Select</button>
        </div>
    </div>
</div>
<!-- Add this custom logout modal -->
<div id="customLogoutModal" class="custom-modal">
    <div class="custom-modal-backdrop"></div>
    <div class="custom-modal-dialog">
        <div class="custom-modal-content">
            <div class="custom-modal-header">
                <h5 class="modal-title">Confirm Logout</h5>
                <span class="custom-modal-close">&times;</span>
            </div>
            <div class="custom-modal-body">
                Are you sure you want to sign off?
            </div>
            <div class="custom-modal-footer">
                <button type="button" class="custom-btn secondary" id="customLogoutCancel">Cancel</button>
                <button type="button" class="custom-btn primary" id="customLogoutConfirm">Sign Out</button>
            </div>
        </div>
    </div>
</div>

<!-- Footer -->
<div th:replace="fragments/admin_page_footer"></div>
<!-- /footer -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
    const container = document.getElementById('data-container');
    const excluded = container.dataset.excluded
    const orgVisaType = container.dataset.orgVisaType
    const orgVisaSeqNo = container.dataset.orgVisaSeqNo
    const userCode = container.dataset.userCode
    const uploadSeqNo = container.dataset.uploadSeqNo
    const exclusionDropdown = document.getElementById('exclusion_selected_remark');
    const regDropdown = document.getElementById('reg_selected_remark');
    const gaoDropdown = document.getElementById('gao_selected_remark');
    const programmeDropdown = document.getElementById('dept_selected_remark');
    const studentId = document.getElementById('studentId').value;

    $(document).ready(function () {
        try {
            //on default hidden all dropdown
            exclusionDropdown.style.display = 'none';
            regDropdown.style.display = 'none';
            gaoDropdown.style.display = 'none';
            programmeDropdown.style.display = 'none';

            var action;
            let selectedFiles = [];
            let selectedFileNames = new Set();
            let deletedFilesSeqNo = [];
            initializePreloadedFiles();

            // Double-click handler for visa history rows
            $('.visa-history-row').on('click', '.view-btn', function () {
                const row = $(this).closest('.visa-history-row');
                const studentId = row.data('student-id');
                const seqNo = row.data('seq-no');
                window.location.href = `/[[${systemContextRoot}]]/admin/visa/viewApprovedStudent?studentId=${studentId}&seqNo=${seqNo}`;
            });

            // Custom Modal Logic
            let currentAction = null;

            // Remark Modal Logic
            let currentRemark = null;

            // Show modal function
            function showCustomModal(action) {
                currentAction = action;
                const modal = document.getElementById('customModal');
                modal.style.display = 'block';

                // Add canvas z-index fix
                document.querySelectorAll('canvas').forEach(canvas => {
                    canvas.style.zIndex = '1';
                });
            }

            // Show modal function
            function showRemarkModal(action) {
                currentRemark = action;
                const modal = document.getElementById('remarkModal');
                modal.style.display = 'block';

                // Add canvas z-index fix
                document.querySelectorAll('canvas').forEach(canvas => {
                    canvas.style.zIndex = '1';
                });
            }

            // Hide modal function
            function hideCustomModal() {
                exclusionDropdown.style.display = 'none';
                regDropdown.style.display = 'none';
                gaoDropdown.style.display = 'none';
                programmeDropdown.style.display = 'none';
                document.getElementById('customModal').style.display = 'none';
            }

            // Hide remark modal function
            function hideRemarkModal() {
                document.getElementById('remarkModal').style.display = 'none';
            }

            // Event Listeners
            document.getElementById('saveButton').addEventListener('click', () => showCustomModal('save'));
            // For DEPT_USER,GAO_USER since it cannot see the button, the event cannot add
            const exclusionRemarkPopupButton = document.getElementById('exclusionRemarkPopupButton');
            if (exclusionRemarkPopupButton) {
                exclusionRemarkPopupButton.addEventListener('click', () => {
                    exclusionDropdown.style.display = 'block';
                    regDropdown.style.display = 'none';
                    gaoDropdown.style.display = 'none';
                    programmeDropdown.style.display = 'none';
                    showRemarkModal('exclusionRemark')
                });
            }
            if (['REG_USER'].indexOf(userCode) !== -1) {
                const approveButton = document.getElementById('approveButton');
                const rejectButton = document.getElementById('rejectButton');
                const regRemarkPopupButton = document.getElementById('regRemarkPopupButton');

                if (approveButton) {
                    approveButton.addEventListener('click', () => showCustomModal('approve'));
                }
                if (rejectButton) {
                    rejectButton.addEventListener('click', () => showCustomModal('reject'));
                }
                if (regRemarkPopupButton) {
                    regRemarkPopupButton.addEventListener('click', () => {
                        exclusionDropdown.style.display = 'none';
                        regDropdown.style.display = 'block';
                        gaoDropdown.style.display = 'none';
                        programmeDropdown.style.display = 'none';
                        showRemarkModal('regRemark')
                    });
                }
                const gaoRemarkPopupButton = document.getElementById('gaoRemarkPopupButton');
                if (gaoRemarkPopupButton) {
                    gaoRemarkPopupButton.addEventListener('click', () => {
                        exclusionDropdown.style.display = 'none';
                        regDropdown.style.display = 'none';
                        gaoDropdown.style.display = 'block';
                        programmeDropdown.style.display = 'none';
                        showRemarkModal('gaoRemark')
                    });
                }
                const programmeRemarkPopupButton = document.getElementById('programmeRemarkPopupButton');
                if (programmeRemarkPopupButton) {
                    programmeRemarkPopupButton.addEventListener('click', () => {
                        exclusionDropdown.style.display = 'none';
                        regDropdown.style.display = 'none';
                        gaoDropdown.style.display = 'none';
                        programmeDropdown.style.display = 'block';
                        showRemarkModal('programmeRemark')
                    });
                }
            } else if (['GAO_USER'].indexOf(userCode) !== -1) {
                const gaoRemarkPopupButton = document.getElementById('gaoRemarkPopupButton');
                if (gaoRemarkPopupButton) {
                    gaoRemarkPopupButton.addEventListener('click', () => {
                        exclusionDropdown.style.display = 'none';
                        regDropdown.style.display = 'none';
                        gaoDropdown.style.display = 'block';
                        programmeDropdown.style.display = 'none';
                        showRemarkModal('gaoRemark')
                    });
                }
            } else if (['DEPT_USER'].indexOf(userCode) !== -1) {
                const programmeRemarkPopupButton = document.getElementById('programmeRemarkPopupButton');
                if (programmeRemarkPopupButton) {
                    programmeRemarkPopupButton.addEventListener('click', () => {
                        exclusionDropdown.style.display = 'none';
                        regDropdown.style.display = 'none';
                        gaoDropdown.style.display = 'none';
                        programmeDropdown.style.display = 'block';
                        showRemarkModal('programmeRemark')
                    });
                }
            }

            // save / approve button confirm modal
            document.getElementById('customConfirmBtn').addEventListener('click', () => {
                hideCustomModal();
                if (currentAction === 'save') {
                    processInfo();
                } else if (currentAction === 'approve') {
                    processInfo();
                } else if (currentAction === 'reject'){
                    processInfo();
                }
            });
            document.getElementById('customCancelBtn').addEventListener('click', hideCustomModal);
            document.querySelector('.custom-close').addEventListener('click', hideCustomModal);
            window.addEventListener('click', (event) => {
                if (event.target === document.getElementById('customModal')) {
                    hideCustomModal();
                }
            });

            // remark modal 
            document.getElementById('remarkSelectBtn').addEventListener('click', () => {
                // let selectedRemark = $("#selected_remark").val()
                let originRemark = ""
                if(currentRemark === "exclusionRemark"){
                    originRemark = $("#exclusionRemark").val()
                    let newRemark = `${originRemark} ${$("#exclusion_selected_remark").val()}`
                    $("#exclusionRemark").val(newRemark.trim())
                }
                else if(currentRemark === "regRemark"){
                    originRemark = $("#regRemark").val()
                    let newRemark = `${originRemark} ${$("#reg_selected_remark").val()}`
                    $("#regRemark").val(newRemark.trim())
                }else if(currentRemark === "gaoRemark"){
                    originRemark = $("#gaoRemark").val()
                    let newRemark = `${originRemark} ${$("#gao_selected_remark").val()}`
                    $("#gaoRemark").val(newRemark.trim())
                }else if(currentRemark === "programmeRemark"){
                    originRemark = $("#programmeRemark").val()
                    let newRemark = `${originRemark} ${$("#dept_selected_remark").val()}`
                    $("#programmeRemark").val(newRemark.trim())
                }
                hideRemarkModal();
            });
            document.getElementById('remarkCancelBtn').addEventListener('click', hideRemarkModal);
            

// Handle ESC key
            document.addEventListener('keydown', (event) => {
                if (event.key === 'Escape') {
                    hideCustomModal();
                }
            });

// PDF Canvas Z-Index Fix (run periodically)
            setInterval(() => {
                document.querySelectorAll('canvas').forEach(canvas => {
                    if (parseInt(canvas.style.zIndex) > 100) {
                        canvas.style.zIndex = '1';
                    }
                });
            }, 500);

            $('#previewContainer').on('click', '.preview-img', function (e){
                e.preventDefault();
                const imgSrc = $(this).attr('src');
                console.log("Image clicked - Length:", imgSrc.length, "First 50 chars:", imgSrc.substring(0, 50));
                if (imgSrc && imgSrc.startsWith('data:image/')) {
                    const mimeType = imgSrc.split(';')[0].split(':')[1];
                    const base64Content = imgSrc.split(',')[1];
                    const blob = base64ToBlob(base64Content, mimeType);
                    if (blob) {
                        const blobUrl = URL.createObjectURL(blob);
                        window.open(blobUrl, '_blank');
                    } else {
                        console.error("Failed to create blob for image:", imgSrc.substring(0, 50));
                    }
                } else {
                    console.error("Invalid image source:", imgSrc);
                }
            });

            $('#previewContainer').on('click', '.pdf-preview', function(e) {
                e.preventDefault();
                const pdfBase64 = $(this).closest('.image-preview').attr('data-base64');
                console.log("PDF clicked - Length:", pdfBase64.length, "First 50 chars:", pdfBase64.substring(0, 50));
                if (pdfBase64 && pdfBase64.startsWith('data:application/pdf;base64,')) {
                    const base64Content = pdfBase64.split(',')[1];
                    const blob = base64ToBlob(base64Content, 'application/pdf');
                    if (blob) {
                        const blobUrl = URL.createObjectURL(blob);
                        window.open(blobUrl, '_blank');
                    } else {
                        console.error("Failed to create blob for PDF:", pdfBase64.substring(0, 50));
                    }
                } else {
                    console.error("Invalid PDF base64 data:", pdfBase64);
                }
            });

            $('#backButton').click(function () {
                // Update session storage before navigating back
                if (sessionStorage.getItem('visa_upload_list_state') || sessionStorage.getItem('visaSearchData')) {
                    updateSessionStorageWithCurrentData();
                }
                window.location.href = "/[[${systemContextRoot}]]/admin/visa/uploadList";
            });

            // Handle browser back button - update session storage before leaving
            window.addEventListener('beforeunload', function() {
                // Only update if there's session data to update and changes might have been made
                if (sessionStorage.getItem('visa_upload_list_state') || sessionStorage.getItem('visaSearchData')) {
                    updateSessionStorageWithCurrentData();
                }
            });

            // Handle browser navigation (back/forward buttons)
            window.addEventListener('popstate', function(event) {
                // Update session storage before navigation
                if (sessionStorage.getItem('visa_upload_list_state') || sessionStorage.getItem('visaSearchData')) {
                    updateSessionStorageWithCurrentData();
                }
            });

            $('#saveButton, #approveButton #rejectButton').click(function () {
                action = this.id; // Store the id of the clicked button (saveButton or approveButton)
                $('#confirmationModal').modal('show'); // Show the confirmation modal
            });

            $('#remarkPopupButton').click(function () {
                action = this.id; // Store the id of the clicked button (saveButton or approveButton)
                $('#remarkModal').modal('show'); // Show the confirmation modal
            });
            function processInfo() {
                let formData = new FormData();
                try{
                    // Get values directly from elements
                    formData.append("studentId", document.getElementById("studentId").textContent);
                    // Validate uploadSeqNo
                    let uploadSeqNo = container.dataset.uploadSeqNo;
                    if (uploadSeqNo && !isNaN(parseInt(uploadSeqNo))) {
                        formData.append("uploadSeqNo", parseInt(uploadSeqNo)); // Send as valid integer
                    } else {
                        formData.append("uploadSeqNo", ""); // Send empty string or omit if null is preferred
                    }
                    formData.append("orgVisaType", container.dataset.orgVisaType);
                    if (orgVisaSeqNo && !isNaN(parseInt(orgVisaSeqNo))) {
                        formData.append("orgVisaSeqNo", parseInt(orgVisaSeqNo)); // Send as valid integer
                    } else {
                        formData.append("orgVisaSeqNo", ""); // Send empty string or omit if null is preferred
                    }
                    // formData.append("orgVisaSeqNo", orgVisaSeqNo ? orgVisaSeqNo : null);
                    // Excluded checkbox value
                    formData.append("submissionStatus", $('#submissionStatus').text())
                    const excludedCheckbox = document.getElementById("excluded");
                    formData.append("excludeStatus", excludedCheckbox.checked ? 'E' : '');
                    formData.append("exclusionRemark", document.getElementById("exclusionRemark")?.value ?? null);
                    if(userCode === "REG_USER"){
                        let visaTypeValue
                        if(document.getElementById("visaType").value === undefined){
                            visaTypeValue = $('#visaType').text();
                        }else{
                            visaTypeValue = document.getElementById("visaType").value
                        }
                        formData.append("visaType", visaTypeValue);

                        let visaNumberValue
                        if(document.getElementById("visaNumber").value === undefined){
                            visaNumberValue = $('#visaNumber').text();
                        }else{
                            visaNumberValue = document.getElementById("visaNumber").value
                        }
                        formData.append("visaNumber", visaNumberValue);

                        let visaExpiryDateValue
                        if(document.getElementById("visaExpiryDate").value === undefined){
                            visaExpiryDateValue = $('#visaExpiryDate').text();
                        }else{
                            visaExpiryDateValue = document.getElementById("visaExpiryDate").value
                        }
                        formData.append("visaExpiryDateTo", visaExpiryDateValue);
                        // formData.append("exclusionRemark", document.getElementById("exclusionRemark")?.value ?? null);
                        formData.append("regRemark", document.getElementById("regRemark")?.value ?? null);
                        formData.append("gaoRemark", document.getElementById("gaoRemark")?.value ?? null);
                        formData.append("programmeRemark", document.getElementById("programmeRemark")?.value ?? null);
                    }else{
                        formData.append("visaType", $('#visaType').text());
                        formData.append("visaNumber", $('#visaNumber').text());
                        formData.append("visaExpiryDateTo", $('#visaExpiryDate').text());
                        // formData.append("exclusionRemark", $('#exclusionRemark').text());
                        formData.append("regRemark", $('#regRemark').text());
                        if(userCode === "DEPT_USER"){
                            formData.append("programmeRemark", document.getElementById("programmeRemark")?.value ?? null);
                            formData.append("gaoRemark", $('#gaoRemark').text());
                        }else if(userCode === "GAO_USER"){
                            formData.append("programmeRemark", $('#programmeRemark').text());
                            formData.append("gaoRemark", document.getElementById("gaoRemark")?.value ?? null);
                        }
                    }
                }catch(e){
                    console.error(`processInfo form error`,e)
                }

                // Determine the action based on the button clicked
                let action = currentAction
                formData.append("action", action);

                // if (selectedFiles && selectedFiles.length > 0) {
                //     for (let file of selectedFiles) {
                //         formData.append("files", file);
                //     }
                // }
                showOverlayLoading();
                $.ajax({
                    url: "/[[${systemContextRoot}]]/admin/visa/ajax/saveStudentView?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                    type: "POST",
                    data: formData,
                    contentType: false,
                    processData: false,
                    success: function (response) {
                        closeOverlayLoading();

                        // Update session storage immediately after successful save
                        updateSessionStorageWithCurrentData();

                        // Now proceed with navigation
                        if (['approve', 'reject'].indexOf(action) != -1) {
                            window.location.href = "/[[${systemContextRoot}]]/admin/visa/uploadList";
                        } else {
                            window.location.reload();
                        }
                    },
                    error: function (xhr, status, error) {
                        closeOverlayLoading();
                        showError("Error saving draft: " + error);
                    }
                });
            }

            // $("#previewContainer").on("click", ".close-btn", function () {
            //     let fileWrapper = $(this).closest(".image-preview");
            //     let fileNameToRemove = fileWrapper.attr("data-filename");
            //     let fileSeqNo = fileWrapper.attr("data-fileSeqNo");
            //
            //     if (fileSeqNo) {
            //         deletedFilesSeqNo.push(fileSeqNo);
            //     }
            //     selectedFiles = selectedFiles.filter(f => f.name !== fileNameToRemove);
            //     selectedFileNames.delete(fileNameToRemove);
            //     fileWrapper.remove();
            //
            //     if (selectedFiles.length > 0) {
            //         $("#saveDraftBtn").show(); // Keep button visible if files remain
            //     } else {
            //         resetPreviewSection();
            //         $("#saveDraftBtn").hide(); // Hide button if no files remain
            //         $("#documentList .document-item").each(function () {
            //             $(this).find(".document-icon")
            //                 .removeClass("uploaded not-uploaded")
            //                 .addClass("not-uploaded");
            //         });
            //     }
            // });

            function resetPreviewSection() {
                $("#previewSection").fadeOut();
                $("#visaEndDate").val("");
                $("#visaNumber").val("");
                $("#dropZone").show();
                $("#fileInput").val("");
                $("#submitBtn").fadeOut();
            }

            function initializePreloadedFiles() {
                $("#previewContainer .image-preview").each(function () {
                    let fileName = $(this).attr("data-filename");
                    let base64Data = $(this).attr("data-base64");

                    if (!base64Data || !base64Data.startsWith("data:")) {
                        console.error("Invalid base64 data for:", fileName);
                        return;
                    }

                    let mimeType = base64Data.startsWith("data:image/") ? "image/jpeg" : "application/pdf";
                    let base64Content = base64Data.split(",")[1];
                    let blob = base64ToBlob(base64Content, mimeType);
                    if (!blob) return;

                    let file = new File([blob], fileName, {type: mimeType});
                    selectedFiles.push(file);
                    selectedFileNames.add(fileName);

                    if (mimeType === "application/pdf") {
                        renderPdfPreview(file, $(this));
                    }
                });
            }

            function renderPdfPreview(file, fileWrapper) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const pdfBlob = new Blob([e.target.result], {type: "application/pdf"});
                    const pdfBlobURL = URL.createObjectURL(pdfBlob);

                    const pdfCanvas = document.createElement("canvas");
                    pdfCanvas.classList.add("pdf-preview");
                    $(pdfCanvas).data("blob-url", pdfBlobURL);

                    pdfjsLib.getDocument(pdfBlobURL).promise.then(pdf => {
                        pdf.getPage(1).then(page => {
                            const viewport = page.getViewport({scale: 1.5});
                            const context = pdfCanvas.getContext('2d');
                            pdfCanvas.height = viewport.height;
                            pdfCanvas.width = viewport.width;

                            const renderContext = {
                                canvasContext: context,
                                viewport: viewport
                            };

                            page.render(renderContext).promise.then(() => {
                                fileWrapper.empty()
                                    // .append($('<span>').text('×').addClass('close-btn'))
                                    .append(pdfCanvas);
                            });
                        });
                    });
                };
                reader.readAsArrayBuffer(file);
            }

            function base64ToBlob(base64, mime) {
                try {
                    const byteCharacters = atob(base64);
                    const byteNumbers = new Array(byteCharacters.length);
                    for (let i = 0; i < byteCharacters.length; i++) {
                        byteNumbers[i] = byteCharacters.charCodeAt(i);
                    }
                    return new Blob([new Uint8Array(byteNumbers)], {type: mime});
                } catch (error) {
                    console.error("Error decoding Base64:", error);
                    return null;
                }
            }

            function showError(message) {
                $("#errorMessage").text(message).fadeIn();
                setTimeout(() => $("#errorMessage").fadeOut(), 5000);
            }

            // $('#additionalFiles').on('change', function (e) {
            //     const files = Array.from(e.target.files);
            //     handleFileSelection(files);
            //     e.target.value = ''; // Reset input
            // });

            // function handleFileSelection(files) {
            //     files.forEach(file => {
            //         if (!isValidFileType(file)) {
            //             alert(`Invalid file type: ${file.name}\nOnly images (JPEG, PNG, GIF) and PDF files are allowed.`);
            //             return;
            //         }
            //         if (!selectedFileNames.has(file.name)) {
            //             selectedFiles.push(file);
            //             selectedFileNames.add(file.name);
            //             renderFilePreview(file);
            //         }
            //     });
            // }

            // function convertFileToBase64(file) {
            //     return new Promise((resolve, reject) => {
            //         const reader = new FileReader();
            //         reader.onload = () => resolve(reader.result.split(',')[1]);
            //         reader.onerror = error => reject(error);
            //         reader.readAsDataURL(file);
            //     });
            // }

            // function isValidFileType(file) {
            //     const allowedTypes = [
            //         'image/jpeg',
            //         'image/png',
            //         'image/gif',
            //         'application/pdf'
            //     ];
            //     return allowedTypes.includes(file.type);
            // }

            // function renderFilePreview(file) {
            //     const reader = new FileReader();
            //     const previewDiv = $('<div class="image-preview"></div>');
            //     // const closeBtn = $('<span class="close-btn">×</span>');
            //
            //     previewDiv.attr('data-filename', file.name);
            //     previewDiv.append(closeBtn);
            //
            //     if (file.type.startsWith('image/')) {
            //         reader.onload = function(e) {
            //             previewDiv.append($('<img>').attr('src', e.target.result).addClass('preview-img'));
            //             $('#previewContainer').append(previewDiv);
            //         };
            //         reader.readAsDataURL(file);
            //     } else if (file.type === 'application/pdf') {
            //         const pdfCanvas = $('<canvas class="pdf-preview"></canvas>');
            //         previewDiv.append(pdfCanvas);
            //         $('#previewContainer').append(previewDiv);
            //         renderPdfPreview(file, previewDiv);
            //     }
            // }
        }catch(e){
            console.error(`on load student view: ${e}`);
        }

    });

    // Logout Modal Logic
    function showLogoutModal() {
        document.getElementById('customLogoutModal').style.display = 'block';
        // Reset canvas z-indexes
        document.querySelectorAll('canvas').forEach(canvas => {
            canvas.style.zIndex = '1';
        });
    }

    function hideLogoutModal() {
        document.getElementById('customLogoutModal').style.display = 'none';
    }

    // Add event listener to the sign off button
    document.querySelector('.btn.btn-light[data-target="#logoutModal"]').addEventListener('click', function (e) {
        e.preventDefault();
        showLogoutModal();
    });

    // Confirm logout action
    document.getElementById('customLogoutConfirm').addEventListener('click', function () {
        // Add your actual logout logic here
        window.location.href = '/logout'; // Example logout action
    });

    // Cancel/close handlers
    document.getElementById('customLogoutCancel').addEventListener('click', hideLogoutModal);
    document.querySelector('#customLogoutModal .custom-modal-close').addEventListener('click', hideLogoutModal);
    document.querySelector('#customLogoutModal .custom-modal-backdrop').addEventListener('click', hideLogoutModal);

    // Handle ESC key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && document.getElementById('customLogoutModal').style.display === 'block') {
            hideLogoutModal();
        }
    });

    // Periodic canvas z-index fix
    setInterval(() => {
        document.querySelectorAll('canvas').forEach(canvas => {
            if (parseInt(canvas.style.zIndex) > 100) {
                canvas.style.zIndex = '1';
            }
        });
    }, 500);

    const bootstrapModal = document.getElementById('logoutModal');
    if (bootstrapModal) {
        bootstrapModal.parentNode.removeChild(bootstrapModal);
    }

    if (typeof jQuery !== 'undefined' && jQuery.fn.modal) {
        jQuery.fn.modal = function () {
        };
    }

    // Update session storage with current form data (no API call needed)
    function updateSessionStorageWithCurrentData() {
        try {
            const studentId = document.getElementById("studentId").textContent;
            console.log(`Updating session storage for student: ${studentId}`);

            // Check for both old and new session storage formats
            const pageStateKey = 'visa_upload_list_state';
            let savedState = sessionStorage.getItem(pageStateKey);
            let dataTableData = [];

            if (savedState) {
                // New format: consolidated state
                const state = JSON.parse(savedState);
                dataTableData = state.tableData || [];
            } else {
                // Old format: separate keys (fallback)
                dataTableData = JSON.parse(sessionStorage.getItem('visaSearchData') || '[]');
            }

            if (dataTableData.length > 0) {
                // Find and update the current student's record
                const studentIndex = dataTableData.findIndex(item => item.studentId === studentId);

                if (studentIndex !== -1) {
                    // Update the record with current form values
                    const updatedRecord = { ...dataTableData[studentIndex] };

                    // Update fields that might have changed
                    const excludedCheckbox = document.getElementById("excluded");
                    updatedRecord.excludeStatus = excludedCheckbox && excludedCheckbox.checked ? 'E' : 'N';
                    updatedRecord.submissionStatus = $('#submissionStatus').text() || updatedRecord.submissionStatus;

                    // Update visa information if user has permission to edit
                    const userCode = "[[${userCode}]]"; // Get userCode from Thymeleaf

                    if (userCode === "REG_USER") {
                        // Update visa type
                        const visaTypeElement = document.getElementById("visaType");
                        if (visaTypeElement) {
                            const visaTypeValue = visaTypeElement.value || $('#visaType').text();
                            if (visaTypeValue) updatedRecord.orgVisaDesc = visaTypeValue;
                        }

                        // Update visa number
                        const visaNumberElement = document.getElementById("visaNumber");
                        if (visaNumberElement) {
                            const visaNumberValue = visaNumberElement.value || $('#visaNumber').text();
                            if (visaNumberValue) updatedRecord.orgVisaNumber = visaNumberValue;
                        }

                        // Update visa expiry date
                        const visaExpiryDateElement = document.getElementById("visaExpiryDate");
                        if (visaExpiryDateElement) {
                            const visaExpiryDateValue = visaExpiryDateElement.value || $('#visaExpiryDate').text();
                            if (visaExpiryDateValue) updatedRecord.orgVisaExpiryDate = visaExpiryDateValue;
                        }

                        // Update remarks
                        const regRemarkElement = document.getElementById("regRemark");
                        const gaoRemarkElement = document.getElementById("gaoRemark");
                        const programmeRemarkElement = document.getElementById("programmeRemark");

                        if (regRemarkElement && regRemarkElement.value !== undefined) {
                            updatedRecord.regRemark = regRemarkElement.value;
                        }
                        if (gaoRemarkElement && gaoRemarkElement.value !== undefined) {
                            updatedRecord.gaoRemark = gaoRemarkElement.value;
                        }
                        if (programmeRemarkElement && programmeRemarkElement.value !== undefined) {
                            updatedRecord.programmeRemark = programmeRemarkElement.value;
                        }
                    } else if (userCode === "DEPT_USER") {
                        // DEPT_USER can only edit programme remarks
                        const programmeRemarkElement = document.getElementById("programmeRemark");
                        if (programmeRemarkElement && programmeRemarkElement.value !== undefined) {
                            updatedRecord.programmeRemark = programmeRemarkElement.value;
                        }
                    } else if (userCode === "GAO_USER") {
                        // GAO_USER can only edit GAO remarks
                        const gaoRemarkElement = document.getElementById("gaoRemark");
                        if (gaoRemarkElement && gaoRemarkElement.value !== undefined) {
                            updatedRecord.gaoRemark = gaoRemarkElement.value;
                        }
                    }

                    // Update exclusion remark if available
                    const exclusionRemarkElement = document.getElementById("exclusionRemark");
                    if (exclusionRemarkElement && exclusionRemarkElement.value !== undefined) {
                        updatedRecord.exclusionRemark = exclusionRemarkElement.value;
                    }

                    // Update the record in the array
                    dataTableData[studentIndex] = updatedRecord;

                    // Save back to session storage in the correct format
                    if (savedState) {
                        // New format: update the consolidated state
                        const state = JSON.parse(savedState);
                        state.tableData = dataTableData;
                        state.timestamp = Date.now(); // Update timestamp
                        sessionStorage.setItem(pageStateKey, JSON.stringify(state));
                        console.log('Updated consolidated session state');
                    } else {
                        // Old format: update separate key (fallback)
                        sessionStorage.setItem('visaSearchData', JSON.stringify(dataTableData));
                        console.log('Updated legacy session storage');
                    }

                    console.log('Session storage updated successfully for student:', studentId);
                    console.log('Updated record:', updatedRecord);
                } else {
                    console.warn('Student record not found in session storage:', studentId);
                }
            } else {
                console.warn('No data found in session storage to update');
            }
        } catch (e) {
            console.error('Failed to update session storage:', e);
        }
    }

    // Make the function globally accessible for testing
    window.updateSessionStorageWithCurrentData = updateSessionStorageWithCurrentData;

    // Debug function to check session storage
    window.debugSessionStorage = function() {
        const pageStateKey = 'visa_upload_list_state';
        const newState = sessionStorage.getItem(pageStateKey);
        const oldState = sessionStorage.getItem('visaSearchData');

        console.log('=== Session Storage Debug ===');
        console.log('New format (visa_upload_list_state):', newState ? JSON.parse(newState) : 'Not found');
        console.log('Old format (visaSearchData):', oldState ? JSON.parse(oldState) : 'Not found');
        console.log('All session storage keys:', Object.keys(sessionStorage));

        return {
            newFormat: newState ? JSON.parse(newState) : null,
            oldFormat: oldState ? JSON.parse(oldState) : null,
            allKeys: Object.keys(sessionStorage)
        };
    };
</script>
</body>
</html>