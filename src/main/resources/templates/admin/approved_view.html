<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/admin_head :: head">
<body>
<!-- Main navbar -->
<div th:replace="fragments/admin_navbar :: navbar"></div>
<!-- /main navbar -->

<!-- Page header -->
<div class="page-header">
    <div class="page-header-content header-elements-md-inline">
        <div class="page-title d-flex">
            <h4><i class="icon-compose mr-2"></i> <span class="font-weight-semibold">Completion list</span></h4>
            <a href="#" class="header-elements-toggle text-default d-md-none navbar-toggler sidebar-mobile-component-toggle"><i class="icon-more"></i></a>
        </div>

        <div class="header-elements d-none py-0 mb-3 mb-md-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a th:href="@{'/'}"><i class="icon-home2 mr-2"></i> Home</a></li>
                    <li class="breadcrumb-item">Visa Management</li>
                    <li class="breadcrumb-item active" aria-current="page">Completion list</li>
                </ol>
            </nav>
        </div>
    </div>
</div>
<!-- /page header -->

<!-- Page content -->
<div class="page-content pt-0">

<!--    <link th:href="@{/assets/css/view_aap_list.css}" rel="stylesheet" type="text/css">-->
    <!-- Main content -->
    <div class="content-wrapper">

        <!-- Content area -->
        <div class="content">

            <!-- Search Criteria -->
            <div class="card">
                <div class="card-header header-elements-inline">
                    <h5 class="card-title"><i class="icon-filter4 mr-2"></i>Search Student Visa Records</h5>
                    <div class="header-elements">
                        <div class="list-icons"></div>
                    </div>
                </div>
                <div class="card-body">
                    <form id="searchForm">
                        <div class="form-group row">
                            <!-- Student ID -->
                            <label for="studentID" class="col-4 col-md-2 col-form-label">Student ID</label>
                            <div class="col-8 col-md-4">
                                <input type="text" class="form-control" name="studentID" id="studentID" placeholder="Enter Student ID">
                            </div>
                        </div>

                        <div class="form-group row">
                            <!--  programme Select -->
                            <label class="col-4 col-md-2 col-form-label">Programme </label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="programCode" id="select_programCode">
                                    <option value="">Please select</option>
                                    <th:block th:each="programme : ${programmes}">
                                        <option th:value="${programme}" th:text="${programme}"></option>
                                    </th:block>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row">
                            <!-- VISA Number -->
                            <label class="col-4 col-md-2 col-form-label">VISA Number</label>
                            <div class="col-8 col-md-4">
                                <input type="text" class="form-control" name="visaNumber" id="select_visaNumber" placeholder="Enter VISA Number">
                            </div>
                        </div>

                        <div class="form-group row">
                            <!-- VISA End Date -->
                            <label class="col-4 col-md-2 col-form-label">VISA End Date</label>
                            <div class="col-8 col-md-8 d-flex">
                                <div class="mr-3">
                                    <label for="visaExpiryDateFr">From</label>
                                    <input type="date" class="form-control" id="visaExpiryDateFr" name="visaExpiryDateFr">
                                </div>
                                <div>
                                    <label for="visaExpiryDateTo">To</label>
                                    <input type="date" class="form-control" id="visaExpiryDateTo" name="visaExpiryDateTo">
                                </div>
                            </div>
                        </div>

                        <div class="form-group row">
                            <!-- VISA Type -->
                            <label class="col-4 col-md-2 col-form-label">VISA Type</label>
                            <div class="col-8 col-md-4">
                                <select class="form-control" name="visaType" id="select_visaType">
                                    <option value="">Please select</option>
                                    <th:block th:each="entry : ${visaTypes}">
                                        <option th:value="${entry['VISATYPECODE']}" th:text="${entry['VISATYPEDESC']}"></option>
                                    </th:block>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <!-- Search and Reset Buttons -->
                            <div class="col-md-8 form-group text-center">
                                <button type="button" class="btn btn-primary" id="btnSearch">
                                    <i class="icon-search4 mr-2"></i> Search
                                </button>
                                <button type="button" class="btn btn-warning" id="btnReset">
                                    <i class="icon-reset mr-2"></i> Reset
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Selection -->
                    <div class="card-body">
                        <form id="selectionForm">
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <div class="table-responsive">
                                        <div class="search-container" style="text-align: right;margin-bottom: 10px; position:absolute;">
                                            <div class="row flex-nowrap">
                                                <!-- <span style="white-space: nowrap; line-height: 34px;" class="h6 mr-3">Filter :</span>
                                                <input type="text" id="searchInput" class="form-control" placeholder="Type to filter...">
-->                                                </div>
                                        </div>
                                        <table id="table_selection" class="table table-sm table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>Student ID</th>
                                                    <th>Student Name</th>
                                                    <th>Faculty</th>
                                                    <th>Programme</th>
                                                    <th>Prog Year</th>
                                                    <th>Admit Term</th>
                                                    <th>VISA Type</th>
                                                    <th>VISA Number</th>
                                                    <th>VISA End Date</th>
                                                    <th></th> <!-- Added column for View button -->
                                                </tr>
                                            </thead>
                                            <tbody></tbody>
                                        </table>
                                    </div>
                                    <div>
                                        <input type="hidden" name="selection_check">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <!-- /Selection -->
            </div>
            <!-- /Search Criteria -->

        </div>
        <!-- /content area -->

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<!-- Footer -->
<div th:replace="fragments/admin_page_footer"></div>
<!-- /footer -->

<!-- Modal for Edit -->
<div id="editModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Meeting Records</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="advisorContainer" class="row"></div>
                <div id="no-results" style="display: none" class="text-center w-100 text-muted">No record found</div>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmSaveModal" tabindex="-1" role="dialog" aria-labelledby="confirmSaveModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmSaveModalLabel">Confirm Save</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                Are you sure you want to save these changes?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmSave">Yes, Save</button>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        var table;
        var pageStateKey = 'approved_view_list_state';

        // State management functions
        function savePageState() {
            if (!table) return;

            // Get current DataTable state
            const dtState = table.state();
            const currentPage = table.page.info();

            const state = {
                // Search form data
                searchCriteria: readSearchCriteriaForm(),
                // DataTable data and state
                tableData: table.data().toArray(),
                tableState: {
                    // Core DataTable state
                    start: dtState ? dtState.start : currentPage.start,
                    length: dtState ? dtState.length : currentPage.length,
                    order: dtState ? dtState.order : table.order(),
                    search: dtState ? dtState.search : { search: table.search() },
                    // Additional page info
                    page: currentPage.page,
                    pages: currentPage.pages,
                    recordsTotal: currentPage.recordsTotal,
                    recordsDisplay: currentPage.recordsDisplay
                },
                // Additional UI state
                timestamp: Date.now()
            };

            // Save to session storage for current session
            try {
                sessionStorage.setItem(pageStateKey, JSON.stringify(state));
                console.log('Page state saved successfully:', {
                    search: state.tableState.search.search,
                    page: state.tableState.page,
                    length: state.tableState.length,
                    recordsTotal: state.tableState.recordsTotal
                });
            } catch (e) {
                console.warn('Failed to save page state:', e);
            }
        }

        function restorePageState() {
            try {
                const savedState = sessionStorage.getItem(pageStateKey);
                if (!savedState) return false;

                const state = JSON.parse(savedState);

                // Check if state is not too old (optional - 1 hour limit)
                if (Date.now() - state.timestamp > 3600000) {
                    sessionStorage.removeItem(pageStateKey);
                    return false;
                }

                // Restore search form
                restoreSearchForm(state.searchCriteria);

                // Restore table data and state
                if (state.tableData && state.tableData.length > 0) {
                    console.log(`Restoring ${state.tableData.length} records from session storage`);
                    initSelection();
                    table.clear().rows.add(state.tableData).draw(false);

                    // Restore table state (pagination, sorting, search, etc.)
                    if (state.tableState) {
                        setTimeout(function() {
                            console.log('Starting DataTable state restoration...');

                            // Step 1: Restore search first (affects filtering)
                            if (state.tableState.search && state.tableState.search.search) {
                                console.log('Restoring search:', state.tableState.search.search);
                                table.search(state.tableState.search.search);
                            }

                            // Step 2: Restore page length (affects pagination)
                            if (state.tableState.length !== undefined) {
                                console.log('Restoring page length:', state.tableState.length);
                                table.page.len(state.tableState.length);
                            }

                            // Step 3: Restore sorting
                            if (state.tableState.order && state.tableState.order.length > 0) {
                                console.log('Restoring order:', state.tableState.order);
                                table.order(state.tableState.order);
                            }

                            // Step 4: Apply changes and then restore page
                            table.draw(false);

                            // Step 5: Restore current page after draw is complete
                            setTimeout(function() {
                                if (state.tableState.page !== undefined && state.tableState.page >= 0) {
                                    console.log('Restoring page:', state.tableState.page);
                                    table.page(state.tableState.page).draw(false);
                                }

                                console.log('DataTable state restoration complete');
                            }, 100);

                        }, 100);
                    }

                    console.log('Page state restored successfully');
                    return true;
                }
            } catch (e) {
                console.warn('Failed to restore page state:', e);
                sessionStorage.removeItem(pageStateKey);
            }
            return false;
        }

        function restoreSearchForm(searchCriteria) {
            if (!searchCriteria) return;

            const $form = $('form#searchForm');
            $form.find('input#studentID').val(searchCriteria.studentId || '');
            $form.find('select#select_programCode').val(searchCriteria.programCode || '');
            $form.find('input#select_visaNumber').val(searchCriteria.visaNumber || '');
            $form.find('input#visaExpiryDateFr').val(searchCriteria.visaExpiryDateFr || '');
            $form.find('input#visaExpiryDateTo').val(searchCriteria.visaExpiryDateTo || '');
            $form.find('select#select_visaType').val(searchCriteria.visaType || '');
        }

        function clearPageState() {
            try {
                sessionStorage.removeItem(pageStateKey);
                console.log('Page state cleared');
            } catch (e) {
                console.warn('Failed to clear page state:', e);
            }
        }
        // Initialize the DataTable
        function initSelection(){
            try{
                if ($.fn.DataTable.isDataTable('#table_selection')) {
                    $('#table_selection').DataTable().clear().destroy();
                }
                var tableElement = $('#table_selection');
                if (tableElement.length === 0) {
                    console.error("Table element not found!");
                    return;
                }
                // Check if the DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#table_selection')) {
                    console.log("DataTable exists, destroying...");
                    $('#table_selection').DataTable().clear().destroy();
                } else {
                    console.log("DataTable does not exist, proceeding to initialize...");
                }
            }catch(e){
                console.error(`initSelection `,e )
            }
            table = $('#table_selection').DataTable({
                responsive: true,
                // scrollX: true,
                //scrollY: '50vh',
                // scrollCollapse: true,
                paging: true,
                pageLength: 25,
                lengthMenu: [5, 10, 25, 50, 100],
                autoWidth: false,
                order: [2, 'asc', 3, 'asc'],
                select: {
                    style: 'multi',
                    selector: 'td:first-child'
                },
                dom: 'Bfrtip',
                buttons: [
                    'print'
                ],
                // Enable state saving for DataTable's built-in features
                stateSave: true,
                stateDuration: 3600, // 1 hour in seconds
                stateLoadCallback: function(settings) {
                    // Don't use DataTable's default state loading, we handle it ourselves
                    return null;
                },
                stateSaveCallback: function(settings, data) {
                    // Don't use DataTable's default state saving, we handle it ourselves
                    return;
                },
                columns: [
                    { data: 'studentId'},
                    { data: 'studentName'},
                    { data: 'faculty'},
                    { data: 'programme'},
                    { data: 'programmeYear'},
                    { data: 'admitTerm'},
                    { data: 'visaType'},
                    { data: 'visaNumber'},
                    {
                        data: 'visaExpiryDate',
                        render: function (data, type, row) {
                            if (type === 'display' || type === 'filter') {
                                // Assuming data is a Unix timestamp in milliseconds
                                if (data) {
                                    var date = new Date(parseInt(data));
                                    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`; // Formats to YYYY-MM-DD
                                }
                                return 'N/A';
                            }
                            return data; // Return raw data for sorting/export
                        }
                    },
                    {
                        data: null,
                        orderable: false,
                        render: function(data, type, row) {
                            var url = '/[[${systemContextRoot}]]/admin/visa/viewApprovedStudent?studentId=' + encodeURIComponent(row.studentId) + '&seqNo=' + encodeURIComponent(row.seqNo);
                            return `<button type="button" class="btn btn-primary view-btn mr-1" onclick="savePageStateAndNavigate('${url}')">View</button>`;
                        }
                    },
                    { data: 'seqNo', visible: false }
                ],
            });

            // Save state when table state changes
            table.on('page.dt length.dt order.dt search.dt draw.dt', function(e, settings) {
                // Only save state for user-initiated changes, not programmatic ones
                if (settings.oFeatures.bServerSide === false) {
                    setTimeout(function() {
                        savePageState();
                        console.log('Table state changed, saved state');
                    }, 150); // Increased delay to ensure all changes are applied
                }
            });

            // Additional event handlers for more comprehensive state tracking
            table.on('column-visibility.dt', function() {
                setTimeout(savePageState, 100);
            });
        }

        // Initialize the page
        function initPage() {
            // Try to restore previous state first
            if (!restorePageState()) {
                // If no state to restore, initialize empty table
                initSelection();
            }
        }

        // Make state management functions available globally for testing
        window.savePageState = savePageState;
        window.restorePageState = restorePageState;
        window.clearPageState = clearPageState;

        // Debug function to check session storage and table state
        window.debugApprovedViewState = function() {
            const savedState = sessionStorage.getItem(pageStateKey);
            const tableData = table ? table.data().toArray() : [];

            console.log('=== Approved View State Debug ===');
            console.log('Session storage state:', savedState ? JSON.parse(savedState) : 'Not found');
            console.log('Current table data count:', tableData.length);
            console.log('Table initialized:', !!table);

            if (tableData.length > 0) {
                console.log('Sample table record:', tableData[0]);
            }

            return {
                sessionState: savedState ? JSON.parse(savedState) : null,
                tableDataCount: tableData.length,
                tableInitialized: !!table,
                sampleRecord: tableData[0] || null
            };
        };

        // Navigation function that saves state before leaving
        window.savePageStateAndNavigate = function(url) {
            savePageState();
            window.location.href = url;
        };

        // Handle browser back button
        window.addEventListener('pageshow', function(event) {
            // This event fires when page is loaded from cache (back button)
            if (event.persisted) {
                console.log('Page loaded from cache, attempting to restore state');
                restorePageState();
            }
        });

        // Save state when user navigates away (including browser back button)
        window.addEventListener('beforeunload', function() {
            savePageState();
        });

        // Handle browser back/forward navigation
        window.addEventListener('popstate', function(event) {
            console.log('Popstate event detected, attempting to restore state');
            setTimeout(function() {
                restorePageState();
            }, 100);
        });

        $('#btnReset').click(function() {
            $('#searchForm')[0].reset(); // Reset all form fields to their default values
            clearPageState(); // Clear saved state when resetting
        });

        // Event listener for search button
        $('#btnSearch').click(function(event){
            event.preventDefault();
            searchMappingResult();
        });

        // Search mapping result
        function searchMappingResult() {
            const validateResults = $('form#searchForm').valid();
            if (!validateResults) {
                return false;
            }
            applySearchCriteriaForm();
            const searchCriteria = readSearchCriteriaForm();
            showOverlayLoading();
            $.ajax({
                type:"POST",
                contentType: "application/json",
                url: "/[[${systemContextRoot}]]/admin/visa/ajax/searchApproved?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                data:JSON.stringify(searchCriteria),
                cache: false,
                timeout: 180000,
                success: function(data) {
                    console.log(`319: `,data)
                    initSelection();
                    console.log(`321`)
                    // console.log(data);
                    const transformedData = transformDataKeys(data);
                    console.log(`324`)
                    // console.log(transformedData);
                    table.clear().rows.add(transformedData).draw();
                    console.log(`326`)

                    // Save state after successful search
                    setTimeout(savePageState, 100); // Small delay to ensure table is fully rendered

                    closeOverlayLoading();
                },
                error: function(e, textStatus, err) {
                    closeOverlayLoading();
                    console.log("e:" + e);
                    console.log("textStatus:" + textStatus);
                    console.log("err:" + err);
                }
            });
        }
        // Search input functionality
        $('#searchInput').on('keyup', function() {
            var searchText = $(this).val().toLowerCase();
            $('#table_selection tr').each(function() {
                var cellText = $(this).text().toLowerCase();
                $(this).toggle(cellText.includes(searchText));
            });
        });

        function applySearchCriteriaForm() {
            const $form = $('form#searchForm');
            studentId = $form.find('input#studentID').val();
            programCode = $form.find('select#select_programCode option:selected').val();
            visaNumber = $form.find('input#select_visaNumber').val();
            visaExpiryDateFr = $form.find('input#visaExpiryDateFr').val();
            visaExpiryDateTo = $form.find('input#visaExpiryDateTo').val();
            visaType = $form.find('select#select_visaType option:selected').val();
        }

        function readSearchCriteriaForm() {
            const $form = $('form#searchForm');
            return {
                studentId: $form.find('input#studentID').val(),
                programCode: $form.find('select#select_programCode option:selected').val(),
                visaNumber: $form.find('input#select_visaNumber').val(),
                visaExpiryDateFr: $form.find('input#visaExpiryDateFr').val(),
                visaExpiryDateTo: $form.find('input#visaExpiryDateTo').val(),
                visaType: $form.find('select#select_visaType option:selected').val()
            };
        }

        function transformDataKeys(data) {
            console.log(`transformDataKeys data: `,data)
            return data.map(item => {
                return {
                    studentId: item.STUDENTID || "", // Provide default empty string if missing
                    studentName: item.STUDENTNAME || "",
                    faculty: item.FACULTY || "",
                    programme: item.PROGRAMME || "",
                    programmeYear: item.PROGRAMMEYEAR || "", // Default to "N/A" if missing
                    admitTerm: item.ADMITTERM || "",
                    visaType: item.VISATYPE || "",
                    visaNumber: item.VISANUMBER || "",
                    visaExpiryDate: item.VISAEXPIRYDATE || "",
                    seqNo: item.SEQNO || ""
                };
            });
        }
        initPage();
    });


</script>
</body>
</html>