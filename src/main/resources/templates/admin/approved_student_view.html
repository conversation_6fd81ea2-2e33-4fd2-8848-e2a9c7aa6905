<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/admin_head :: head">
</head>
<style>
    .preview-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 15px;
    }
    .image-preview {
        position: relative;
        margin: 5px;
        display: inline-block;
    }
    .preview-img, .pdf-preview {
        max-width: 100px;
        border-radius: 5px;
        border: 1px solid #ddd;
    }
    /*.modal {*/
    /*    display: none;*/
    /*    position: fixed;*/
    /*    z-index: 1000;*/
    /*    left: 0;*/
    /*    top: 0;*/
    /*    width: 100%;*/
    /*    height: 100%;*/
    /*    background-color: rgba(0, 0, 0, 0.7);*/
    /*    text-align: center;*/
    /*}*/
    /*.modal-content {*/
    /*    max-width: 90%;*/
    /*    max-height: 90%;*/
    /*    display: block;*/
    /*    margin: auto;*/
    /*    object-fit: contain;*/
    /*    margin-top: 3%;*/
    /*}*/
    /*.modal-close {*/
    /*    position: absolute;*/
    /*    top: 10px;*/
    /*    right: 20px;*/
    /*    font-size: 30px;*/
    /*    color: white;*/
    /*    cursor: pointer;*/
    /*}*/
    /*.modal-close:hover {*/
    /*    color: red;*/
    /*}*/
    /* Rest of your styles remain unchanged */
</style>
<body>
<!-- Main navbar -->
<div th:replace="fragments/admin_navbar :: navbar"></div>
<!-- Page content -->
<div class="page-content pt-0">
    <div class="content-wrapper">
        <div class="content">
            <div class="card">
                <div class="card-header header-elements-inline">
                    <h5 class="card-title"><i class="icon-user mr-2"></i>Approval Visa Details</h5>
                </div>
                <div class="card-body">
                    <div class="student-details-container">
                        <div class="row">
                            <div class="col-8">
                                <p><strong>Student ID:</strong> <span th:text="${studentDetails.studentId}"></span></p>
                                <p><strong>Student Name:</strong> <span th:text="${studentDetails.studentName}"></span></p>
                                <p><strong>Faculty:</strong> <span th:text="${studentDetails.faculty}"></span></p>
                                <p><strong>Programme:</strong> <span th:text="${studentDetails.programme}"></span></p>
                                <p><strong>Programme Year:</strong> <span th:text="${studentDetails.programmeYear != null ? studentDetails.programmeYear : 'N/A'}"></span></p>
                                <p><strong>Admit Term:</strong> <span th:text="${studentDetails.admitTerm}"></span></p>
                                <p><strong>Visa Type:</strong> <span th:text="${visaUploadInfo.vtypeDesc}"></span></p>
                                <p><strong>Visa Number:</strong> <span th:text="${visaUploadInfo.visaNumber}"></span></p>
                                <p><strong>VISA End Date:</strong> <span th:text="${visaUploadInfo.visaExpiryDate}"></span></p>
                            </div>
                            <div class="col-4">
                                <div class="form-group">
                                    <div class="preview-container" id="previewContainer">
                                        <th:block th:each="file : ${visaUploadInfo?.uploadedFiles}">
                                            <div class="image-preview"
                                                 th:attr="data-filename=${file.filename}, data-fileSeqNo=${file.fileSeqNo}, data-base64='data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}">
                                                <th:block th:if="${file.mimetype.startsWith('image/')}">
                                                    <img th:src="'data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}" class="preview-img" alt="Uploaded Image"/>
                                                </th:block>
                                                <th:block th:if="${file.mimetype == 'application/pdf'}">
                                                    <canvas class="pdf-preview"></canvas>
                                                </th:block>
                                            </div>
                                        </th:block>
                                    </div>
<!--                                    <div class="attachment-controls">-->
<!--                                        <label for="additionalFiles" class="add-file-btn">-->
<!--                                            <div class="plus-icon">+</div>-->
<!--                                            <span>Attach Additional Files</span>-->
<!--                                        </label>-->
<!--                                        <input type="file" id="additionalFiles" multiple style="display: none;">-->
<!--                                    </div>-->
                                </div>
                            </div>
                            <div class="row col-md-4"></div>
                            <div class="row col-md-4">
                                <div class="form-group text-center">
                                    <button id="backButton" type="button" class="btn btn-secondary">Back to Student List</button>
                                </div>
                            </div>
                            <div class="row col-md-4"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<!--<div id="imageModal" class="modal">-->
<!--    <span id="closeImageModal" class="modal-close">×</span>-->
<!--    <img id="modalImage" class="modal-content">-->
<!--</div>-->
<!--<div id="pdfModal" class="modal">-->
<!--    <span id="closePdfModal" class="modal-close">×</span>-->
<!--    <iframe id="pdfFrame" class="modal-content" style="width: 80%; height: 80%;"></iframe>-->
<!--</div>-->
<!--&lt;!&ndash; Custom Modal remains unchanged &ndash;&gt;-->
<!--<div id="customModal" class="custom-modal">-->
<!--    &lt;!&ndash; ... &ndash;&gt;-->
<!--</div>-->

<!-- Footer -->
<div th:replace="fragments/admin_page_footer"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
    $(document).ready(function () {
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.worker.min.js';

        let selectedFiles = [];
        let selectedFileNames = new Set();
        let deletedFilesSeqNo = [];

        initializePreloadedFiles();

        $('#previewContainer').on('click', '.preview-img', function(e) {
            e.preventDefault();
            const imgSrc = $(this).attr('src');
            console.log("Image clicked - Length:", imgSrc.length, "First 50 chars:", imgSrc.substring(0, 50));
            if (imgSrc && imgSrc.startsWith('data:image/')) {
                const mimeType = imgSrc.split(';')[0].split(':')[1];
                const base64Content = imgSrc.split(',')[1];
                const blob = base64ToBlob(base64Content, mimeType);
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for image:", imgSrc.substring(0, 50));
                }
            } else {
                console.error("Invalid image source:", imgSrc);
            }
        });

        $('#previewContainer').on('click', '.pdf-preview', function(e) {
            e.preventDefault();
            const pdfBase64 = $(this).closest('.image-preview').attr('data-base64');
            console.log("PDF clicked - Length:", pdfBase64.length, "First 50 chars:", pdfBase64.substring(0, 50));
            if (pdfBase64 && pdfBase64.startsWith('data:application/pdf;base64,')) {
                const base64Content = pdfBase64.split(',')[1];
                const blob = base64ToBlob(base64Content, 'application/pdf');
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for PDF:", pdfBase64.substring(0, 50));
                }
            } else {
                console.error("Invalid PDF base64 data:", pdfBase64);
            }
        });

        $('#backButton').click(function () {
            window.location.href = "/[[${systemContextRoot}]]/admin/visa/approved";
        });

        function initializePreloadedFiles() {
            $("#previewContainer .image-preview").each(function () {
                let fileName = $(this).attr("data-filename");
                let base64Data = $(this).attr("data-base64");

                console.log("Initializing file:", fileName, "Base64 length:", base64Data.length, "First 50 chars:", base64Data.substring(0, 50));

                if (!base64Data || !base64Data.startsWith("data:")) {
                    console.error("Invalid base64 data for:", fileName);
                    $(this).text("No preview available for " + fileName);
                    return;
                }

                let mimeType = base64Data.startsWith("data:image/") ? base64Data.split(';')[0].split(':')[1] : "application/pdf";
                let base64Content = base64Data.split(",")[1];
                let blob = base64ToBlob(base64Content, mimeType);
                if (!blob) return;

                let file = new File([blob], fileName, {type: mimeType});
                selectedFiles.push(file);
                selectedFileNames.add(fileName);

                if (mimeType === "application/pdf") {
                    renderPdfPreview(file, $(this));
                }
            });
        }

        function renderPdfPreview(file, fileWrapper) {
            const reader = new FileReader();
            reader.onload = function (e) {
                const pdfBlob = new Blob([e.target.result], {type: "application/pdf"});
                const pdfBlobURL = URL.createObjectURL(pdfBlob);

                const pdfCanvas = fileWrapper.find('.pdf-preview')[0];
                $(pdfCanvas).data('blob-url', pdfBlobURL);

                pdfjsLib.getDocument(pdfBlobURL).promise.then(pdf => {
                    pdf.getPage(1).then(page => {
                        const scale = 0.5;
                        const viewport = page.getViewport({scale: scale});
                        const context = pdfCanvas.getContext('2d');
                        pdfCanvas.height = viewport.height;
                        pdfCanvas.width = viewport.width;

                        const renderContext = {
                            canvasContext: context,
                            viewport: viewport
                        };

                        page.render(renderContext).promise.then(() => {
                            console.log("PDF preview rendered for:", file.name);
                        });
                    });
                }).catch(error => {
                    console.error("Error rendering PDF:", error);
                    fileWrapper.text("Error loading preview for " + file.name);
                });
            };
            reader.readAsArrayBuffer(file);
        }

        function base64ToBlob(base64, mime) {
            try {
                const byteCharacters = atob(base64);
                const byteNumbers = new Array(byteCharacters.length);
                for (let i = 0; i < byteCharacters.length; i++) {
                    byteNumbers[i] = byteCharacters.charCodeAt(i);
                }
                return new Blob([new Uint8Array(byteNumbers)], {type: mime});
            } catch (error) {
                console.error("Error decoding Base64:", error);
                return null;
            }
        }
    });
</script>
</body>
</html>