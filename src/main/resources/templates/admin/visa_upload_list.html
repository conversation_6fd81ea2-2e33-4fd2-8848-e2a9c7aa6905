<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/admin_head :: head">
<body>
    <!-- Main navbar -->
    <div th:replace="fragments/admin_navbar :: navbar"></div>
    <!-- /main navbar -->

	<!-- Page header -->
	<div class="page-header">
		<div class="page-header-content header-elements-md-inline">
			<div class="page-title d-flex">
				<h4><i class="icon-compose mr-2"></i> <span class="font-weight-semibold">Visa Extension List</span></h4>
				<a href="#" class="header-elements-toggle text-default d-md-none navbar-toggler sidebar-mobile-component-toggle"><i class="icon-more"></i></a>
			</div>

            <div class="header-elements d-none py-0 mb-3 mb-md-0">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a th:href="@{'/'}"><i class="icon-home2 mr-2"></i> Home</a></li>
                        <li class="breadcrumb-item">Visa Management</li>
                        <li class="breadcrumb-item active" aria-current="page">Visa Extension List</li>
                    </ol>
                </nav>
            </div>
		</div>
	</div>
	<!-- /page header -->

    <!-- Page content -->
    <div class="page-content pt-0">

<!--        <link th:href="@{/assets/css/view_aap_list.css}" rel="stylesheet" type="text/css">-->
        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Content area -->
            <div class="content">

                <!-- Search Criteria -->
                <div class="card">
                    <div class="card-header header-elements-inline">
                        <h5 class="card-title"><i class="icon-filter4 mr-2"></i>Search Student Visa Records</h5>
                        <div class="header-elements">
                            <div class="list-icons"></div>
                        </div>
                    </div>
                    <div class="card-body">
                        <form id="searchForm">
                            <div class="form-group row">
                                <!-- Student ID -->
                                <label for="studentID" class="col-4 col-md-2 col-form-label">Student ID</label>
                                <div class="col-8 col-md-4">
                                    <input type="text" class="form-control" name="studentID" id="studentID" placeholder="Enter Student ID">
                                </div>
                            </div>

                            <div class="form-group row">
                                <!--  programme Select -->
                                <label class="col-4 col-md-2 col-form-label">Programme </label>
                                <div class="col-8 col-md-4">
                                    <select class="form-control" name="programCode" id="select_programCode" multiple="multiple">
                                        <option value="">Please select</option>
                                        <th:block th:each="programme : ${programmes}">
                                            <option th:value="${programme}" th:text="${programme}"></option>
                                        </th:block>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- VISA Number -->
                                <label class="col-4 col-md-2 col-form-label">VISA Number</label>
                                <div class="col-8 col-md-4">
                                    <input type="text" class="form-control" name="visaNumber" id="select_visaNumber" placeholder="Enter VISA Number">
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- VISA End Date -->
                                <label class="col-4 col-md-2 col-form-label">VISA End Date</label>
                                <div class="col-8 col-md-8 d-flex">
                                    <div class="mr-3">
                                        <label for="visaExpiryDateFr">From</label>
                                        <input type="date" class="form-control" id="visaExpiryDateFr" name="visaExpiryDateFr">
                                    </div>
                                    <div>
                                        <label for="visaExpiryDateTo">To</label>
                                        <input type="date" class="form-control" id="visaExpiryDateTo" name="visaExpiryDateTo">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- VISA Type -->
                                <label class="col-4 col-md-2 col-form-label">VISA Type</label>
                                <div class="col-8 col-md-4">
                                    <select class="form-control" name="visaType" id="select_visaType">
                                        <option value="">Please select</option>
                                        <th:block th:each="entry : ${visaTypes}">
                                            <option th:value="${entry['VISATYPECODE']}" th:text="${entry['VISATYPEDESC']}"></option>
                                        </th:block>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- VISA Submission Status -->
                                <label class="col-4 col-md-2 col-form-label">VISA Submission Status</label>
                                <div class="col-8 col-md-4">
                                    <select class="form-control" name="submissionStatus" id="select_submissionStatus">
                                        <option value="">Please select</option>
                                        <option value="PENDING">Pending</option>
                                        <option value="DRAFT">Draft</option>
                                        <option value="SUBMITTED">Submitted</option>
                                        <!-- <option value="APPROVED">Approved</option> -->
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- Excluded? -->
                                <label class="col-4 col-md-2 col-form-label">Excluded?</label>
                                <div class="col-8 col-md-4">
                                    <select class="form-control" name="excluded" id="select_excluded" required>
                                        <option value="ALL">All</option>
                                        <option value="Y">Yes</option>
                                        <option value="N">No</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row">
                                <!-- Search and Reset Buttons -->
                                <div class="col-md-8 form-group text-center">
                                    <button type="button" class="btn btn-primary" id="btnSearch">
                                        <i class="icon-search4 mr-2"></i> Search
                                    </button>
                                    <button type="button" class="btn btn-warning" id="btnReset">
                                        <i class="icon-reset mr-2"></i> Reset
                                    </button>
                                </div>
                            </div>
                        </form>

                        <!-- Selection -->
                        <div class="card-body">
                            <form id="selectionForm">
                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <div class="search-container" style="text-align: right;margin-bottom: 10px; position:absolute;">
                                                <div class="row flex-nowrap">
                                                    <!-- <span style="white-space: nowrap; line-height: 34px;" class="h6 mr-3">Filter :</span>
                                                    <input type="text" id="searchInput" class="form-control" placeholder="Type to filter...">
    -->                                                </div>
                                            </div>
                                            <table id="table_selection" class="table table-sm table-striped table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Student ID</th>
                                                        <th>Student Name</th>
                                                        <th>Faculty</th>
                                                        <th>Programme</th>
                                                        <th>Prog Year</th>
                                                        <th>Year Status</th>
                                                        <th>Year Status Eff Date</th>
                                                        <th>Admit Term</th>
                                                        <th>VISA Type</th>
                                                        <th>VISA End Date</th>
                                                        <th>VISA Expiry Status</th>
                                                        <th>Excluded?</th>
                                                        <th>REG Remark</th>
                                                        <th>Submission Status</th>
                                                        <th></th>
                                                    </tr>
                                                </thead>
                                                <tbody></tbody>
                                            </table>
                                        </div>
                                        <div>
                                            <input type="hidden" name="selection_check">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /Selection -->
                </div>
                <!-- /Search Criteria -->

            </div>
            <!-- /content area -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->

    <!-- Footer -->
    <div th:replace="fragments/admin_page_footer"></div>
    <!-- /footer -->

    <!-- Modal for Edit -->
    <div id="editModal" class="modal" tabindex="-1" role="dialog">
      <div class="modal-dialog" role="document">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">Meeting Records</h5>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
              <div id="advisorContainer" class="row"></div>
              <div id="no-results" style="display: none" class="text-center w-100 text-muted">No record found</div>
          </div>
        </div>
      </div>
    </div>

    <script>
    $(document).ready(function() {
        var table;
        var originalData = {};
        var pageStateKey = 'visa_upload_list_state';

        // State management functions
        function savePageState() {
            if (!table) return;

            // Get current DataTable state
            const dtState = table.state();
            const currentPage = table.page.info();

            const state = {
                // Search form data
                searchCriteria: readSearchCriteriaForm(),
                // DataTable data and state
                tableData: table.data().toArray(),
                tableState: {
                    // Core DataTable state
                    start: dtState ? dtState.start : currentPage.start,
                    length: dtState ? dtState.length : currentPage.length,
                    order: dtState ? dtState.order : table.order(),
                    search: dtState ? dtState.search : { search: table.search() },
                    // Additional page info
                    page: currentPage.page,
                    pages: currentPage.pages,
                    recordsTotal: currentPage.recordsTotal,
                    recordsDisplay: currentPage.recordsDisplay
                },
                // Additional UI state
                timestamp: Date.now()
            };

            // Save to session storage for current session
            try {
                sessionStorage.setItem(pageStateKey, JSON.stringify(state));
                console.log('Page state saved successfully:', {
                    search: state.tableState.search.search,
                    page: state.tableState.page,
                    length: state.tableState.length,
                    recordsTotal: state.tableState.recordsTotal
                });
            } catch (e) {
                console.warn('Failed to save page state:', e);
            }
        }

        function restorePageState() {
            try {
                const savedState = sessionStorage.getItem(pageStateKey);
                if (!savedState) return false;

                const state = JSON.parse(savedState);

                // Check if state is not too old (configurable time limit)
                const EXPIRATION_TIME = 3600000; // 1 hour = 3600000ms
                // Other options:
                // const EXPIRATION_TIME = 1800000;  // 30 minutes
                // const EXPIRATION_TIME = 7200000;  // 2 hours
                // const EXPIRATION_TIME = 86400000; // 24 hours
                // const EXPIRATION_TIME = 0;        // Never expire (until browser tab closes)

                if (EXPIRATION_TIME > 0 && Date.now() - state.timestamp > EXPIRATION_TIME) {
                    sessionStorage.removeItem(pageStateKey);
                    return false;
                }

                // Restore search form
                restoreSearchForm(state.searchCriteria);

                // Restore table data and state
                if (state.tableData && state.tableData.length > 0) {
                    console.log(`Restoring ${state.tableData.length} records from session storage`);
                    initSelection();
                    table.clear().rows.add(state.tableData).draw(false);

                    // Restore table state (pagination, sorting, search, etc.)
                    if (state.tableState) {
                        // Use multiple timeouts to ensure proper sequencing
                        setTimeout(function() {
                            console.log('Starting DataTable state restoration...');

                            // Step 1: Restore search first (affects filtering)
                            if (state.tableState.search && state.tableState.search.search) {
                                console.log('Restoring search:', state.tableState.search.search);
                                table.search(state.tableState.search.search);
                            }

                            // Step 2: Restore page length (affects pagination)
                            if (state.tableState.length !== undefined) {
                                console.log('Restoring page length:', state.tableState.length);
                                table.page.len(state.tableState.length);
                            }

                            // Step 3: Restore sorting
                            if (state.tableState.order && state.tableState.order.length > 0) {
                                console.log('Restoring order:', state.tableState.order);
                                table.order(state.tableState.order);
                            }

                            // Step 4: Apply changes and then restore page
                            table.draw(false);

                            // Step 5: Restore current page after draw is complete
                            setTimeout(function() {
                                if (state.tableState.page !== undefined && state.tableState.page >= 0) {
                                    console.log('Restoring page:', state.tableState.page);
                                    table.page(state.tableState.page).draw(false);
                                } else if (state.tableState.start !== undefined && state.tableState.length !== undefined) {
                                    const pageNumber = Math.floor(state.tableState.start / state.tableState.length);
                                    console.log('Restoring page (calculated):', pageNumber);
                                    table.page(pageNumber).draw(false);
                                }

                                console.log('DataTable state restoration complete');
                                debugCurrentState();
                            }, 100);

                        }, 100);
                    }

                    console.log('Page state restored successfully');
                    return true;
                }
            } catch (e) {
                console.warn('Failed to restore page state:', e);
                sessionStorage.removeItem(pageStateKey);
            }
            return false;
        }

        function restoreSearchForm(searchCriteria) {
            if (!searchCriteria) return;

            const $form = $('form#searchForm');
            $form.find('input#studentID').val(searchCriteria.studentId || '');
            $form.find('input#select_visaNumber').val(searchCriteria.visaNumber || '');
            $form.find('input#visaExpiryDateFr').val(searchCriteria.visaExpiryDateFr || '');
            $form.find('input#visaExpiryDateTo').val(searchCriteria.visaExpiryDateTo || '');
            $form.find('select#select_visaType').val(searchCriteria.visaType || '');
            $form.find('select#select_submissionStatus').val(searchCriteria.submissionStatus || '');
            $form.find('select#select_excluded').val(searchCriteria.excluded || 'ALL');

            // Handle multi-select for program codes
            if (searchCriteria.programCode && Array.isArray(searchCriteria.programCode)) {
                $form.find('select[name="programCode"]').val(searchCriteria.programCode);
            }
        }

        function clearPageState() {
            try {
                sessionStorage.removeItem(pageStateKey);
                console.log('Page state cleared');
            } catch (e) {
                console.warn('Failed to clear page state:', e);
            }
        }

        // Debug function to check current state
        function debugCurrentState() {
            if (!table) {
                console.log('Table not initialized');
                return;
            }

            const pageInfo = table.page.info();
            const currentState = {
                search: table.search(),
                page: pageInfo.page,
                length: pageInfo.length,
                start: pageInfo.start,
                order: table.order(),
                recordsTotal: pageInfo.recordsTotal,
                recordsDisplay: pageInfo.recordsDisplay
            };

            console.log('Current DataTable State:', currentState);
            return currentState;
        }

        // Make debug function available globally
        window.debugTableState = debugCurrentState;

        // Make state management functions available globally for testing
        window.savePageState = savePageState;
        window.restorePageState = restorePageState;
        window.clearPageState = clearPageState;

        // Debug function to check session storage and table state
        window.debugVisaListState = function() {
            const savedState = sessionStorage.getItem(pageStateKey);
            const tableData = table ? table.data().toArray() : [];

            console.log('=== Visa List State Debug ===');
            console.log('Session storage state:', savedState ? JSON.parse(savedState) : 'Not found');
            console.log('Current table data count:', tableData.length);
            console.log('Table initialized:', !!table);

            if (tableData.length > 0) {
                console.log('Sample table record:', tableData[0]);
            }

            return {
                sessionState: savedState ? JSON.parse(savedState) : null,
                tableDataCount: tableData.length,
                tableInitialized: !!table,
                sampleRecord: tableData[0] || null
            };
        };

        // Navigation function that saves state before leaving
        window.savePageStateAndNavigate = function(url) {
            savePageState();
            window.location.href = url;
        };

        // Handle browser back button
        window.addEventListener('pageshow', function(event) {
            // This event fires when page is loaded from cache (back button)
            if (event.persisted) {
                console.log('Page loaded from cache, attempting to restore state');
                restorePageState();
            }
        });

        // Save state when user navigates away (including browser back button)
        window.addEventListener('beforeunload', function() {
            savePageState();
        });

        // Handle browser back/forward navigation
        window.addEventListener('popstate', function(event) {
            console.log('Popstate event detected, attempting to restore state');
            setTimeout(function() {
                restorePageState();
            }, 100);
        });

        // Initialize the DataTable
        function initSelection(){
            try{
                if ($.fn.DataTable.isDataTable('#table_selection')) {
                    $('#table_selection').DataTable().clear().destroy();
                }
                console.log("Initializing DataTable...");
                var tableElement = $('#table_selection');
                if (tableElement.length === 0) {
                    console.error("Table element not found!");
                    return;
                }
                console.log("Table element:", tableElement);

                // Check if the DataTable is already initialized
                if ($.fn.DataTable.isDataTable('#table_selection')) {
                    console.log("DataTable exists, destroying...");
                    $('#table_selection').DataTable().clear().destroy();
                } else {
                    console.log("DataTable does not exist, proceeding to initialize...");
                }
            }catch (e){
                console.error(`initSelection `,e )
            }

            table = $('#table_selection').DataTable({
                responsive: true,
                autoWidth: false,
                paging: true,
                pageLength: 25,
                lengthMenu: [5, 10, 25, 50, 100],
                // order: [1, 'asc'],
                dom: 'Bfrtip',
                // Enable state saving for DataTable's built-in features
                stateSave: true,
                stateDuration: 3600, // 1 hour in seconds (3600s = 1 hour)
                // Other options: 1800 (30 min), 7200 (2 hours), 86400 (24 hours), -1 (never expire)
                stateLoadCallback: function(settings) {
                    // Don't use DataTable's default state loading, we handle it ourselves
                    return null;
                },
                stateSaveCallback: function(settings, data) {
                    // Don't use DataTable's default state saving, we handle it ourselves
                    return;
                },
                buttons: [
                    {
                        extend: 'print',
                        text: '<i class="icon-printer mr-2"></i> Print', // Add printer icon
                        className: 'btn btn-primary mr-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
                            format: {
                                body: function(data, row, column, node) {
                                    if (column === 11) {
                                        const checkbox = $(node).find('input.upload-checkbox');
                                        return checkbox.prop('checked') ? 'Yes' : 'No';
                                    }
                                    return data;
                                }
                            }
                        }
                    },
                    {
                        extend: 'excel',
                        text: '<i class="icon-file-excel mr-2"></i> Export to Excel',
                        title: 'Visa Extension List',
                        className: 'btn btn-primary mr-1',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31], // All except View (13), plus yearStatus (27), just in order the mapping in the body case switch part
                            format: {
                                header: function(data, columnIdx) {
                                    console.log(`header `, data, columnIdx)
                                    const customHeaders = [
                                        'Student ID',                      // 0
                                        'Name',                            // 1
                                        'Prog Code',                       // 2
                                        'Prog Title',                      // 3
                                        'Prog Year',                       // 4
                                        'Admit Term',                      // 5
                                        'Student Status',                  // 6
                                        'Year Status Eff Date',                  // 7
                                        'VISA Type',                       // 8
                                        'VISA Description',                // 9
                                        'Expiry Date',                     // 10
                                        'Residence',                       // 11
                                        'Expiry Status',                   // 12
                                        '10 weeks reminder send date',     // 13
                                        '8 weeks reminder send date',      // 14
                                        'EdUHK Email',                     // 15
                                        'Personal Email',                  // 16
                                        'Area Code',                       // 17
                                        'Phone',                           // 18
                                        'Address 1',                       // 19
                                        'Address 2',                       // 20
                                        'Address 3',                       // 21
                                        'City',                            // 22
                                        'Country',                         // 23
                                        'Faculty',                         // 24
                                        'Prog Office Email',               // 25
                                        'Prog Office Contact No',          // 26
                                        'Exclude?',                        // 27
                                        'Excluded Reason',                  // 28
                                        'REG remark',                  // 29
                                        'GAO remark',                  // 30
                                        'Prog Office remark',                  // 31
                                    ];
                                    return customHeaders[columnIdx] || data; //sine there are some column display on frontend will not display on excel, || data to ensure get all header in table
                                },
                                body: function (data, row, column, node) {
                                    // console.log('Export row data:', row); // Log the row object
                                    let rowData = table.row(row).data();
                                    switch (column){
                                        case 0:
                                            return rowData.studentId;
                                        case 1:
                                            return rowData.studentName;
                                        case 2:
                                            return rowData.programme;
                                        case 3:
                                            return rowData.programTitle;
                                        case 4:
                                            return rowData.programmeYear;
                                        case 5:
                                            return rowData.admitTerm;
                                        case 6:
                                            return rowData.yearStatus;
                                        case 7:
                                            return rowData.effectiveDate;
                                        case 8:
                                            return rowData.orgVisaType;
                                        case 9:
                                            return rowData.orgVisaDesc;
                                        case 10:
                                            return rowData.orgVisaExpiryDate;
                                        case 11:
                                            return rowData.residence;
                                        case 12:
                                            return rowData.expiryStatus;
                                        case 13:
                                            return rowData.reminder1Date;
                                        case 14:
                                            return rowData.reminder2Date;
                                        case 15:
                                            return rowData.eduhkEmail;
                                        case 16:
                                            return rowData.personalEmail;
                                        case 17:
                                            return rowData.phoneArea;
                                        case 18:
                                            return rowData.phoneNumber;
                                        case 19:
                                            return rowData.addr1;
                                        case 20:
                                            return rowData.addr2;
                                        case 21:
                                            return rowData.addr3;
                                        case 22:
                                            return rowData.city;
                                        case 23:
                                            return rowData.country;
                                        case 24:
                                            return rowData.faculty;
                                        case 25:
                                            return rowData.programOfficeEmail;
                                        case 26:
                                            return rowData.programOfficePhone;
                                        case 27:
                                            return rowData.excludeStatus === 'E' ? 'Y' : 'N';
                                        case 28:
                                            return rowData.exclusionRemark;
                                        case 29:
                                            return rowData.regRemark;
                                        case 30:
                                            return rowData.gaoRemark;
                                        case 31:
                                            return rowData.programmeRemark;
                                        default:
                                            return "";
                                            break;
                                    }
                                }
                            }
                        }
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="icon-file-pdf mr-2"></i> Export to PDF',
                        title: 'Visa Extension List',
                        className: 'btn btn-primary mr-1',
                        orientation: 'landscape',
                        exportOptions: {
                            columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], // All columns except View (12)
                            format: {
                                header: function(data, columnIdx) {
                                    const customHeaders = [
                                        'Student ID',          // 0
                                        'Student Name',        // 1
                                        'Faculty',             // 2
                                        'Programme',           // 3
                                        'Prog Year',           // 4
                                        'Year Status',         // 5
                                        'Year Status Eff Date',// 6
                                        'Admit Term',          // 7
                                        'VISA Type',           // 8
                                        'VISA End Date',        // 9
                                        'VISA Expiry Status',  // 10
                                        'Excluded?',           // 11
                                        'REG Remark',           // 12
                                        'Submission Status'    // 13
                                    ];
                                    return customHeaders[columnIdx];
                                },
                                body: function(data, row, column, node) {
                                    if (column === 11) {
                                        const checkbox = $(node).find('input.upload-checkbox');
                                        return checkbox.prop('checked') ? 'Yes' : 'No';
                                    }
                                    return data;
                                }
                            }
                        },
                        customize: function(doc) {
                            // Adjust column widths (12 columns, all auto)
                            doc.content[1].table.widths = Array(14).fill('auto'); // 12 'auto' entries
                            // Ensure table fits within page margins
                            doc.styles.tableHeader.fontSize = 10;
                            doc.defaultStyle.fontSize = 8;
                        }
                    }
                ],
                columns: [
                    { data: 'studentId' },                                  // 0
                    { data: 'studentName' },                                // 1
                    { data: 'faculty' },                                    // 2
                    { data: 'programme' },                                  // 3
                    { data: 'programmeYear' },                              // 4
                    { data: 'yearStatus' },                                 // 5
                    { data: 'effectiveDate' },                                 // 5
                    { data: 'admitTerm' },                                  // 6
                    { data: 'orgVisaDesc' },                                 // 7
                    { data: 'orgVisaExpiryDate' },                          // 8
                    { data: 'expiryStatus' },                  // 9
                    {
                        data: 'excludeStatus',
                        render: function(data, type, row) {
                            if (type === 'display') {
                                return `<input type="checkbox" class="upload-checkbox" ${data === 'E' ? 'checked' : ''} disabled>`;
                            }
                            return data === 'E' ? 'E' : 'N';
                        },
                        type: 'string'
                    },                                                      // 10
                    { data: 'regRemark' },                           // 11
                    { data: 'submissionStatus' },                           // 12
                    {
                        data: null,
                        orderable: false,
                        render: function(data, type, row) {
                            // Construct the URL for redirection
                            var url = '/[[${systemContextRoot}]]/admin/visa/viewStudent?studentId=' + row.studentId + '&program=' + row.programme;
                            return `<button type="button" class="btn btn-primary edit-btn mr-1" onclick="savePageStateAndNavigate('${url}')">View</button>`;
                        }
                    },                                                      //13
                    { data: 'residence', visible: false },                  // 14
                    { data: 'exclusionRemark', visible: false },            // 15
                    { data: 'gaoRemark', visible: false },                  // 16
                    { data: 'programmeRemark', visible: false },            // 17
                    { data: 'excludeRemarksUserstamp', visible: false },  // 18
                    { data: 'gaoRemarkUserstamp', visible: false },         // 19
                    { data: 'programmeRemarkUserstamp', visible: false },         // 20
                    { data: 'excludeRemarksTimestamp', visible: false },   // 21
                    { data: 'gaoRemarkTimestamp', visible: false },         // 22
                    { data: 'programmeRemarkTimestamp', visible: false },         // 23
                    { data: 'reminder1Date', visible: false },              // 24
                    { data: 'reminder2Date', visible: false },              // 25
                    { data: 'personalEmail', visible: false },              // 26
                    { data: 'phoneArea', visible: false },                  // 27
                    { data: 'phoneNumber', visible: false },                 // 28
                    { data: 'addr1', visible: false },                               // 29
                    { data: 'addr2', visible: false },                               // 30
                    { data: 'addr3', visible: false },                 // 31
                ],
            });

            // Save state when table state changes
            table.on('page.dt length.dt order.dt search.dt draw.dt', function(e, settings) {
                // Only save state for user-initiated changes, not programmatic ones
                if (settings.oFeatures.bServerSide === false) {
                    setTimeout(function() {
                        savePageState();
                        console.log('Table state changed, saved state');
                    }, 150); // Increased delay to ensure all changes are applied
                }
            });

            // Additional event handlers for more comprehensive state tracking
            table.on('column-visibility.dt', function() {
                setTimeout(savePageState, 100);
            });
        }

        // Initialize the page
        function initPage() {
            // Try to restore previous state first
            if (!restorePageState()) {
                // If no state to restore, initialize empty table
                initSelection();
            }
        }

        $('#btnReset').click(function() {
            $('#searchForm')[0].reset(); // Reset all form fields to their default values
            clearPageState(); // Clear saved state when resetting
        });

        // Event listener for search button
        $('#btnSearch').click(function(event){
            event.preventDefault();
            searchMappingResult();
        });

        // Search mapping result
        function searchMappingResult() {
            const validateResults = $('form#searchForm').valid();
            if (!validateResults) {
                return false;
            }
            const searchCriteria = readSearchCriteriaForm();
            showOverlayLoading();
            $.ajax({
                type:"POST",
                contentType: "application/json",
                url: "/[[${systemContextRoot}]]/admin/visa/ajax/search?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
                data:JSON.stringify(searchCriteria),
                cache: false,
                timeout: 180000,
                success: function(data) {
                    try{
                        initSelection();
                        // const transformedData = transformDataKeys(data);
                        // table.clear().rows.add(transformedData).draw();
                        table.clear().rows.add(data).draw();

                        // Save state after successful search
                        setTimeout(savePageState, 100); // Small delay to ensure table is fully rendered
                    }catch(e){
                        console.error(`search visa record error `,e);
                    }
                    closeOverlayLoading();
                },
                error: function(e, textStatus, err) {
                    closeOverlayLoading();
                    if (textStatus == 'timeout') {
                        showNotySimple('warning', 'There are too many results for this search. Please modify your search.', {timeout: 30000});
                    }
                    else if(e.status == '422'){
                        showNotySimple('error', e.responseText);
                    }
                    else if(e.status == '405'){
                        //sessTimeOut();
                        console.error(`search visa record error `,e);
                        showNotySimple('error', e.responseText);
                    } else {
                        console.log("e:" + e);
                        console.log("textStatus:" + textStatus);
                        console.log("err:" + err);
                        showNotySimple('error', 'Your action cannot be processed.<br>Please refresh.<br>If the problem persists, please contact system support.', {timeout: false});
                    }
                }
            });
        }

        // Event delegation for edit and delete buttons
        $('#table_selection').on('click', '.edit-btn', function() {
            var rowData = table.row($(this).closest('tr')).data();
            // Navigate to student view page instead of showing modal
            var url = '/[[${systemContextRoot}]]/admin/visa/viewStudent?studentId=' + rowData.studentId + '&program=' + row.programme;
            savePageStateAndNavigate(url);
        });

        // Function to show the edit modal and populate it with row data
        function showEditModal(rowData) {
            const container = $('#advisorContainer');
            container.empty();

            let recordNum = 0;

            rowData.meetingRecords.forEach(advisor => {

                if (!advisor.MEETINGROUND){
                    return;
                }
                const meetingDate = moment(new Date(advisor.MEETINGDATE)).format("DD/MM/YYYY HH:mm");
                const creationDate = moment(new Date(advisor.CREATIONDATE)).format("DD/MM/YYYY HH:mm");

                const advisorSubmittedForm =
                    `<div class="col-sm-12"><div class="advisor-item">
                    <div class="info-row">
                        <div class="info-label">Meeting Date</div>
                        <div class="text-right">${meetingDate}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Faculty</div>
                        <div class="text-right">${advisor.FACULTY}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Meeting Mode</div>
                        <div class="text-right">${advisor.MEETINGMODE}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Meeting Round</div>
                        <div class="text-right">${advisor.MEETINGROUND}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Student Turn up the Meeting?</div>
                        <div class="text-right">${formatYesNo(advisor.MEETINGTURNUP)}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Received The Student Information Sheet or DegreeWorks Study Plan Before/In the Meeting? </div>
                        <div class="text-right">${formatYesNo(advisor.STUDENTPLANRECEIVED)}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Observation of your meeting with the student</div>
                        <div class="text-right">${advisor.OBSERVATION}</div>
                    </div>
                    <div class="info-row">
                        <div class="info-label">Created Date</div>
                        <div class="text-right">${creationDate}</div>
                    </div>
                </div></div>`;
                container.append(advisorSubmittedForm);
                recordNum++;
            });

            if (recordNum === 0) {
                $('#no-results').show();
            }else{
                $('#no-results').hide();
            }
            $('#editModal').modal('show');
        }

        // Search input functionality
        $('#searchInput').on('keyup', function() {
            var searchText = $(this).val().toLowerCase();
            $('#table_selection tr').each(function() {
                var cellText = $(this).text().toLowerCase();
                $(this).toggle(cellText.includes(searchText));
            });
        });

        function readSearchCriteriaForm() {
            const $form = $('form#searchForm');
            return {
                studentId: $form.find('input#studentID').val(),
                programCode: $form.find('select[name="programCode"]').val() || [],
                visaNumber: $form.find('input#select_visaNumber').val(),
                visaExpiryDateFr: $form.find('input#visaExpiryDateFr').val(),
                visaExpiryDateTo: $form.find('input#visaExpiryDateTo').val(),
                visaType: $form.find('select#select_visaType option:selected').val(),
                submissionStatus: $form.find('select#select_submissionStatus option:selected').val(),
                excluded: $form.find('select#select_excluded').val()
            };
            // const formData = {};
            // formData.studentId = $form.find('input#studentID').val();  // Student ID
            // formData.programCode = $form.find('select#select_programCode option:selected').val();  // Programme
            // formData.visaNumber = $form.find('input#select_visaNumber').val();  // VISA Number
            // formData.visaExpiryDateFr = $form.find('input#visaExpiryDateFr').val();  // VISA Expiry From Date
            // formData.visaExpiryDateTo = $form.find('input#visaExpiryDateTo').val();  // VISA Expiry To Date
            // formData.visaType = $form.find('select#select_visaType option:selected').val();  // VISA Type
            // formData.submissionStatus = $form.find('select#select_submissionStatus option:selected').val();  // VISA Submission Status
            // formData.excluded = $form.find('select#select_excluded').val();  // Excluded? (true/false)
            // return formData;
        }

        // function transformDataKeys(data) {
        //     return data.map(item => {
        //         return {
        //             studentId: item.STUDENTID || "", // Provide default empty string if missing
        //             studentName: item.STUDENTNAME || "",
        //             faculty: item.FACULTY || "",
        //             programme: item.PROGRAMME || "",
        //             progYear: item.PROGYEAR || "", // Default to "N/A" if missing
        //             admitTerm: item.ADMITTERM || "",
        //             visaType: item.VISATYPE || "",
        //             visaNumber: item.VISANUMBER || "",
        //             visaExpiryDate: item.VISAEXPIRYDATE || "",
        //             visaExpiryStatus: item.VISAEXPIRYSTATUS || "",
        //             uploadRequired: item.UPLOADREQUIRED === 'Y' ? 'N' : 'Y'
        //         };
        //     });
        // }

        initPage();

        // Initialize page state management
        console.log('Page state management initialized');
    });


    </script>
</body>
</html>