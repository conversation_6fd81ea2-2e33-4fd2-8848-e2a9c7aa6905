<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>
<link th:href="@{/assets/css/visaupload.css}" rel="stylesheet" type="text/css">
<body class="page-login-index">

<section class="vsm-login-section">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 text-center mb-5">
            </div>
        </div>
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-8 login-form">
                <div class="login-wrap p-4 p-md-5">
                    <!-- Error message display -->
                    <div th:if="${error}" class="alert alert-danger text-center">
                        <p th:text="${error}"></p>
                    </div>
                    <!-- Preview Page -->
                    <div id="previewSection" class="card-body ajaxForm">
                        <form id="visaDetailsForm">
                            <!-- Back Button -->
                            <a href="javascript:history.back();" class="login-back-button">
                                <i class="fas fa-arrow-left"></i> Back
                            </a>
                            <div class="icon d-flex align-items-center justify-content-center">
                                <img th:src="@{/assets/images/eduhk_logo.png}" alt="EdUHK" class="left"/>
                            </div>
                            <h3 class="text-center mb-4">Please check if the submitted visa data is correct:</h3>
                            <div id="errorMessage" class="alert alert-danger text-center" style="display: none;"></div>
                            <div class="card-body ajaxForm">
                                <div id="container">
                                    <div class="visa-info-item">
                                        <label for="visaCode">Visa Code:</label>
                                        <input type="text" class="form-control" id="visaCode" name="visaCode"
                                               th:value="${visaCode}" readonly>
                                    </div>
                                    <div class="visa-info-item">
                                        <label for="isStudent">Is Student:</label>
                                        <input type="text" class="form-control" id="isStudent" name="isStudent"
                                               th:value="${isStudent}" readonly>
                                    </div>
                                    <div class="visa-info-item">
                                        <label for="visaEndDate">Visa End Date:</label>
                                        <input type="text" class="form-control" id="visaEndDate" name="visaEndDate"
                                               th:value="${visaEndDate}" readonly>
                                    </div>
                                    <div class="visa-info-item">
                                        <label for="visaNumber">Visa Number:</label>
                                        <input type="text" class="form-control" id="visaNumber" name="visaNumber"
                                               th:value="${visaNumber}" readonly>
                                    </div>
                                </div>
                                <div class="text-center">
                                    <button type="button" id="confirmBtn" class="btn btn-success login-button"
                                            style="margin-top: 10px; width: 45%;" onclick="confirmSubmission()">Confirm</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
    function confirmSubmission() {
        $.ajax({
            url: "/[[${systemContextRoot}]]/submitVisa/updateStatus?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                visaCode: $("#visaCode").val(),
                isStudent: $("#isStudent").val(),
                visaEndDate: $("#visaEndDate").val(),
                visaNumber: $("#visaNumber").val()
            }),
            success: function(response) {
                alert('Visa data saved successfully!');
                window.location.href = '/';
            },
            error: function(xhr, status, error) {
                alert('Error saving visa data.');
            }
        });
    }
</script>
</body>
</html>
