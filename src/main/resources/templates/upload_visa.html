<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head th:include="fragments/head :: head"></head>
<style>
    /* Existing CSS remains unchanged */
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2000;
        justify-content: center;
        align-items: center;
    }
    .loading-overlay img {
        width: 50px;
        height: 50px;
    }
    body {
        background: #f8f9fd;
    }
    .drop-zone {
        border: 2px dashed #007bff; /* Modern blue border */
        padding: 25px; /* Slightly tighter padding */
        background: #f5f8ff; /* Softer blue background */
        border-radius: 12px; /* Smoother corners */
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); /* Subtle shadow for depth */
        transition: all 0.3s ease; /* Smooth hover effect */
        cursor: pointer;
        text-align: center; /* Center the CTA */
    }

    .drop-zone:hover {
        background: #e6efff; /* Light blue on hover */
        border-color: #0056b3; /* Darker blue border */
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
    }

    .drop-zone.drag-hover {
         background: #cce4ff; /* Brighter blue when dragging */
         border: 2px solid #0056b3; /* Solid, darker blue border */
         box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15); /* Larger shadow */
         transform: scale(1.02); /* Slight scale-up for emphasis */
         opacity: 0.95; /* Subtle fade effect */
     }

    .upload-cta {
        margin-bottom: 20px; /* Clear separation from guidelines */
    }

    .upload-cta p {
        font-size: 18px; /* Larger for emphasis */
        color: #007bff; /* Matches border color */
        margin: 0; /* Remove default <p> margin */
    }

    .upload-guidelines {
        text-align: left; /* Left-align guidelines */
        background: #ffffff; /* White background for contrast */
        padding: 15px; /* Inner padding */
        border-radius: 8px; /* Rounded corners */
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05); /* Subtle inset shadow */
    }

    .upload-guidelines p {
        margin: 5px 0; /* Tighter spacing between lines */
        font-size: 14px; /* Slightly smaller for hierarchy */
        color: #333; /* Darker text for readability */
    }

    .upload-guidelines strong {
        color: #0056b3; /* Darker blue for emphasis */
    }
    .preview-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 15px;
    }
    .preview-img {
        max-width: 100px !important;
        max-height: 100px !important;
    }
    .image-preview {
        position: relative;
        margin: 5px;
        display: inline-block;
    }
    .preview-img, .pdf-preview canvas {
        max-width: 100px;
        border-radius: 5px;
        cursor: pointer;
        border: 1px solid #ddd;
    }
    .close-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background: red;
        color: white;
        font-size: 14px;
        width: 20px;
        height: 20px;
        text-align: center;
        line-height: 18px;
        border-radius: 50%;
        cursor: pointer;
    }
    @media (max-width: 768px) {
        .upload-container {
            width: 95%;
            padding: 15px;
        }
    }
    .document-list {
        margin-top: 15px;
        margin-bottom: 20px;
    }
    .document-item {
        display: flex;
        align-items: center;
        margin: 8px 0;
    }
    .document-icon {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        display: inline-block;
    }
    .not-uploaded {
        background: url([[@{/assets/images/not-uploaded.png}]]) no-repeat center; 
        background-size: contain;
    }
    .uploaded {
        background: url([[@{/assets/images/uploaded.png}]]) no-repeat center;
        background-size: contain;
    }
    .upload-instruction {
        font-size: 14px;
        color: #0056b3;
        font-weight: 600;
        padding-bottom: 5px;
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .hidden {
        display: none;
    }

</style>
<body class="page-login-index">
    <main>
        <div id="loadingOverlay" class="loading-overlay">
            <img th:src="@{/assets/images/eduhk_loading2.gif}" alt="Loading...">
        </div>
        <section class="vsm-login-section">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 text-center mb-5">
                    </div>
                </div>
                <div class="row justify-content-center">
                    <div class="col-md-8 col-lg-8 login-form">
                        <div class="login-wrap p-4 p-md-5">
                            <div th:replace="fragments/header :: header"></div>
                            <div id="head-container">
                                <div class="row">
                                    <div class="col-md-6">
                                        <a href="#" class="nav-link dropdown-toggle" data-toggle="dropdown">
                                            <span>User Guide</span>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-left">
                                            <a th:href="@{/pdf/Student VMS user guide V.3 (ENG).pdf}" target="_blank" class="dropdown-item">English</a>
                                            <a th:href="@{/pdf/Student VMS user guide V.2 (簡體).pdf}" target="_blank" class="dropdown-item">简体</a>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div th:replace="fragments/logout :: logout"></div>
                                    </div>
                                </div>
                            </div>
                            
<!--                            <div th:replace="fragments/student_scripts :: body"></div>-->
                            <!-- Upload Section -->
                            <div id="uploadSection">
                                <h3 class="text-center mb-4">Visa Upload</h3>
                                <div class="card-body ajaxForm">
                                    <form id="frmConfirm" class="form-horizontal" method="POST" enctype="multipart/form-data">
                                        <div id="container">
                                            <input type="hidden" name="pidm" th:value="${visaInfo?.pidm}">
                                            <input type="hidden" name="seqNo" th:value="${visaUploadInfo?.seqNo}">
                                            <input type="hidden" id="isMainlandStudent" th:value="${visaInfo?.isMainlandStudent}">
                                            <input type="hidden" id="uploadedDocs" name="uploadedDocs" th:value="${visaUploadInfo?.extractedData}">
                                            <input type="hidden" id="residence" name="residence" th:value="${visaInfo?.residence}">

                                            <!-- Student Info Display -->
                                            <div class="form-group student-info">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="row">
                                                            <!-- Student Details Card -->
                                                            <div class="col-md-6 mb-4">
                                                                <div class="info-card">
                                                                    <h5 class="info-card-title">Student Details</h5>
                                                                    <dl class="info-card-content">
                                                                        <div class="info-item">
                                                                            <dt>Student ID</dt>
                                                                            <dd th:text="${visaInfo?.studentId ?: 'N/A'}"></dd>
                                                                        </div>
                                                                        <div class="info-item">
                                                                            <dt>Student Name</dt>
                                                                            <dd th:text="${visaInfo?.studentName ?: 'N/A'}"></dd>
                                                                        </div>
                                                                        <div class="info-item">
                                                                            <dt>Programme</dt>
                                                                            <dd th:text="${visaInfo?.programme ?: 'N/A'}"></dd>
                                                                        </div>
                                                                    </dl>
                                                                </div>
                                                            </div>
                                                            <!-- Current Visa Details Card -->
                                                            <div class="col-md-6 mb-4">
                                                                <div class="info-card">
                                                                    <h5 class="info-card-title">Current Visa Details</h5>
                                                                    <dl class="info-card-content">
                                                                        <div class="info-item">
                                                                            <dt>Visa Type</dt>
                                                                            <dd th:text="${visaInfo?.visaTypeDesc ?: 'N/A'}"></dd>
                                                                        </div>
                                                                        <div class="info-item">
                                                                            <dt>Visa Number</dt>
                                                                            <dd th:text="${visaInfo?.visaNumber ?: 'N/A'}"></dd>
                                                                        </div>
                                                                        <div class="info-item">
                                                                            <dt>VISA End Date</dt>
                                                                            <dd th:text="${visaInfo?.visaExpiryDate != null ? #temporals.format(visaInfo?.visaExpiryDate, 'yyyy-MM-dd') : 'N/A'}"></dd>
                                                                        </div>
                                                                    </dl>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <!-- Dropdown List -->
                                            <div class="form-group">
                                                <label for="visaType">Select Visa Type:</label>
                                                <select id="visaType" name="visaCode" class="form-control" th:value="${visaUploadInfo?.vtypeCode}">
                                                    <option value="">Please select</option>
                                                </select>
                                                <span class="upload-instruction">Please submit the document(s) below.</span>
                                                <div id="documentList" class="document-list"></div>
                                            </div>
                                            <div id="errorMessage" class="alert alert-danger text-center" style="display: none;"></div>
                                            <!-- Drag & Drop File Upload -->
                                            <div class="form-group">
                                                <div class="drop-zone" id="dropZone">
                                                    <input type="file" id="fileInput" name="files" multiple accept=".pdf,.jpg,.jpeg,.png" hidden>
                                                    <div class="upload-cta">
                                                        <p><strong>Drop files here or click to upload</strong></p>
                                                    </div>
                                                    <div class="upload-guidelines">
                                                        <p><strong>Allowed Formats:</strong> PDF, JPG, PNG</p>
                                                        <p><strong>Max File Size:</strong> 5MB each</p>
                                                        <p><strong>Image Limit:</strong> Up to 3 images total</p>
                                                        <p><strong>Note:</strong> Each PDF page counts as one image (e.g., a 2-page PDF = 2 images), so keep it at 3 or fewer.</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- File Preview -->
                                            <div class="form-group">
                                                <div class="preview-container" id="previewContainer">
                                                    <th:block th:each="file : ${visaUploadInfo?.uploadedFiles}">
                                                        <div class="image-preview" th:attr="data-filename=${file.filename}, data-fileSeqNo=${file.fileSeqNo}, data-base64='data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}">
                                                            <span class="close-btn">×</span>
                                                            <th:block th:if="${file.mimetype.startsWith('image/')}">
                                                                <img th:src="'data:' + ${file.mimetype} + ';base64,' + ${file.base64Encoded}"
                                                                    class="preview-img" alt="Uploaded Image"/>
                                                            </th:block>
                                                            <th:block th:if="${file.mimetype == 'application/pdf'}">
                                                                <!-- Canvas will be added by JS -->
                                                            </th:block>
                                                        </div>
                                                    </th:block>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>

                            <!-- Preview Section -->
                            <div id="previewSection" class="card-body ajaxForm" th:style="${not #lists.isEmpty(visaUploadInfo?.uploadedFiles)} ? 'display: block;' : 'display: none;'">
                                <h3 class="text-center mb-4">Please check if the submitted visa data is correct:</h3>
                                <form id="visaDetailsForm">
                                    <div class="form-group">
                                        <label for="studentName">Student Name:</label>
                                        <input type="text" class="form-control" id="studentName" name="studentName"
                                               th:value="${visaUploadInfo?.studentName}" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label for="institutionName">Institution Name:</label>
                                        <input type="text" class="form-control" id="institutionName" name="institutionName"
                                               th:value="${visaUploadInfo?.institutionName}" readonly>
                                    </div>
                                    <div class="form-group">
                                        <label for="visaEndDate">Visa End Date:</label>
                                        <input type="date" class="form-control" id="visaEndDate" name="visaEndDate"
                                            th:value="${#dates.format(visaUploadInfo?.visaExpiryDate, 'yyyy-MM-dd')}" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="visaNumber">Visa Number:</label>
                                        <input type="text" class="form-control" id="visaNumber" name="visaNumber"
                                            th:value="${visaUploadInfo?.visaNumber}" required>
                                    </div>
                                </form>
                            </div>

                            <!-- Buttons Container -->
                            <div class="text-center buttons-container" style="margin-top: 10px;">
                                <button type="button" id="saveDraftBtn" class="btn btn-primary action-button"
                                        style="width: 45%;">Save as Draft</button>
                                <button type="button" id="submitBtn" class="btn btn-primary action-button"
                                        style="width: 45%; margin-left: 10px; display: none;" onclick="saveAsDraft(true)">Submit and Save</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

<!-- Image Modal -->
<!--<div id="imageModal" class="modal">-->
<!--    <span id="closeImageModal" class="modal-close">×</span>-->
<!--    <img id="modalImage" class="modal-content">-->
<!--</div>-->

<!--&lt;!&ndash; PDF Modal &ndash;&gt;-->
<!--<div id="pdfModal" class="modal">-->
<!--    <span id="closePdfModal" class="modal-close">×</span>-->
<!--    <iframe id="pdfFrame" class="modal-content" style="width: 80%; height: 80%;"></iframe>-->
<!--</div>-->

<!-- Footer -->
<div th:replace="fragments/page_footer"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.16.105/pdf.min.js"></script>
<script>
    let selectedFiles = [];
    let selectedFileNames = new Set();
    let deletedFilesSeqNo = [];
    let documents = []; // Array to hold document status: {code, uploaded, required}

    // Parse extractedData into documents array
    function parseExtractedData(data) {
        if (!data || data.trim() === '') return [];
        return data.split(',').map(item => {
            const [code, uploaded, required] = item.split(':');
            // Name will be populated later from getVisaDocuments if not already set
            return {
                code: code,
                name: '',
                uploaded: uploaded === 'U',
                required: required === 'R'
            };
        });
    }

    // Serialize documents array back to string
    function serializeExtractedData() {
        return documents.map(doc => `${doc.code}:${doc.uploaded ? 'U' : 'N'}:${doc.required ? 'R' : 'O'}`).join(',');
    }

    // Show error message
    function showError(message) {
        $("#errorMessage").text(message).fadeIn();
        setTimeout(() => $("#errorMessage").fadeOut(), 10000); //10s
    }

    $(document).ready(function () {
        $("#errorMessage").hide();
        $("#previewSection").hide();
        $("#saveDraftBtn").hide();

        // Initialize preloaded files and documents
        initializePreloadedFiles();
        let extractedData = "[[${visaUploadInfo?.extractedData != null ? visaUploadInfo?.extractedData : ''}]]";
        documents = parseExtractedData(extractedData);

        $('#previewContainer').on('click', '.pdf-preview', function(e) {
            e.preventDefault();
            const base64 = $(this).closest('.image-preview').attr('data-base64');
            if (base64 && base64.startsWith('data:application/pdf;base64,')) {
                const base64Content = base64.split(',')[1];
                const blob = base64ToBlob(base64Content, 'application/pdf');
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for PDF.");
                }
            }
        });

        $('#previewContainer').on('click', '.preview-img', function (e) {
            e.preventDefault();
            const imgSrc = $(this).attr('src');
            if (imgSrc && imgSrc.startsWith('data:image/')) {
                const mimeType = imgSrc.split(';')[0].split(':')[1];
                const base64Content = imgSrc.split(',')[1];
                const blob = base64ToBlob(base64Content, mimeType);
                if (blob) {
                    const blobUrl = URL.createObjectURL(blob);
                    window.open(blobUrl, '_blank');
                } else {
                    console.error("Failed to create blob for image.");
                }
            } else {
                console.error("Invalid image source:", imgSrc);
            }
        });

        // Remove file
        $("#previewContainer").on("click", ".close-btn", function () {
            let fileWrapper = $(this).closest(".image-preview");
            let fileNameToRemove = fileWrapper.attr("data-filename");
            let fileSeqNo = fileWrapper.attr("data-fileSeqNo");

            if (fileSeqNo) deletedFilesSeqNo.push(fileSeqNo);
            selectedFiles = selectedFiles.filter(f => f.name !== fileNameToRemove);
            selectedFileNames.delete(fileNameToRemove);
            fileWrapper.remove();

            if (selectedFiles.length > 0) {
                checkVisaFileForChatGPT(selectedFiles, processVisaData, function(error) {
                    showError(error || "Failed to reprocess files after removal.");
                });
                $("#saveDraftBtn").show();
            } else {
                // Reset to initial state when no files remain
                let visaType = $("#visaType").val();
                documents = []; // Clear documents array
                resetPreviewSection();
                $("#saveDraftBtn").hide();
                $("#uploadedDocs").val(""); // Clear serialized data
                deletedFilesSeqNo = []; // Clear deleted files
                if (visaType) {
                    loadRequiredDocuments(visaType, function() {
                        let missingDocs = getMissingDocuments();
                        if (missingDocs.length > 0) {
                            showError("All files removed. Required documents: " + missingDocs.join(", ") + ".");
                        }
                    });
                } else {
                    $("#documentList").empty();
                    showError("All files removed. Please select a visa type.");
                }
            }
        });

        // Drag-and-Drop Handlers for #dropZone
        $("#dropZone").on({
            dragover: function (event) {
                event.preventDefault(); // Allow drop
                event.stopPropagation();
                $(this).addClass('drag-hover'); // Optional: Add hover style
            },
            dragleave: function (event) {
                event.preventDefault();
                event.stopPropagation();
                $(this).removeClass('drag-hover');
            },
            drop: function (event) {
                event.preventDefault();
                event.stopPropagation();
                $(this).removeClass('drag-hover');
                let files = event.originalEvent.dataTransfer.files; // Get dropped files
                if (files.length > 0) {
                    handleFiles(files); // Reuse existing handler
                }
            }
        });

        // Dropzone click
        $("#dropZone").on("click", function (event) {
            event.stopPropagation();
            if (!$(event.target).is("#fileInput")) document.getElementById("fileInput").click();
        });

        // File input change
        $("#fileInput").on("click", function () {
            // Optional: Clear the value to allow re-selection
            $(this).val("");
        }).on("change", function (event) {
            //block limit 4 in select
            if (selectedFiles.length >= 3) {
                showError("You can upload a maximum of 3 files.");
                return;
            }
            let files = Array.from(event.target.files);
            if (files.length > 0) {
                handleFiles(files);
                // Clear the input after processing to allow re-selection
                $(this).val("");
            }
        });

        $("#saveDraftBtn").on("click", function() {
            saveAsDraft(false);
        });

        // Load visa types and initial documents
        loadVisaTypes(function() {
            if (selectedFiles.length > 0 && checkAllRequiredDocumentsValid()) {
                $("#previewSection").show();
                $("#submitBtn").show();
                $("#saveDraftBtn").show();
            }
            renderDocumentList();
        });
    });

    // Initialize preloaded files
    function initializePreloadedFiles() {
        $("#previewContainer .image-preview").each(function () {
            let fileName = $(this).attr("data-filename");
            let fileSeqNo = $(this).attr("data-fileSeqNo");
            let base64Data = $(this).attr("data-base64");

            if (!base64Data || !base64Data.startsWith("data:")) return;
            let mimeType = base64Data.startsWith("data:image/") ? "image/jpeg" : "application/pdf";
            let base64Content = base64Data.split(",")[1];
            let blob = base64ToBlob(base64Content, mimeType);
            if (!blob) return;

            let file = new File([blob], fileName, { type: mimeType });
            selectedFiles.push(file);
            selectedFileNames.add(fileName);

            if (mimeType === "application/pdf") renderPdfPreview(file, $(this));
        });
    }

    // Render PDF preview
    function renderPdfPreview(file, fileWrapper) {
        let reader = new FileReader();
        reader.onload = function (e) {
            let pdfBlob = new Blob([e.target.result], { type: "application/pdf" });
            let pdfBlobURL = URL.createObjectURL(pdfBlob);

            let pdfCanvas = document.createElement("canvas");
            pdfCanvas.classList.add("pdf-preview");

            pdfjsLib.getDocument(pdfBlobURL).promise.then(pdf => {
                pdf.getPage(1).then(page => {
                    let viewport = page.getViewport({ scale: 1.5 });
                    pdfCanvas.width = viewport.width;
                    pdfCanvas.height = viewport.height;

                    let renderContext = {
                        canvasContext: pdfCanvas.getContext("2d"),
                        viewport: viewport
                    };

                    page.render(renderContext).promise.then(() => {
                        // Add canvas + close button
                        fileWrapper.empty().append(
                            $("<span>").text("×").addClass("close-btn")
                        ).append(pdfCanvas);

                        // 💡 Set the onclick **after** attaching the canvas
                        $(pdfCanvas).on('click', function () {
                            window.open(pdfBlobURL, '_blank');
                        });
                    });
                });
            });
        };
        reader.readAsArrayBuffer(file);
    }

    // Handle file uploads
    async function handleFiles(files) {
        let visaType = $("#visaType").val();
        if (!visaType) {
            showError("Please select a visa type before uploading.");
            return;
        }

        const MAX_FILE_SIZE_MB = 5;
        const MAX_TOTAL_FILES = 3;
        const previewContainer = $("#previewContainer");
        let newFiles = Array.from(files);
        let totalFilesCount = selectedFiles.length + newFiles.length;

        if (totalFilesCount > MAX_TOTAL_FILES) {
            showError(`You can upload a maximum of ${MAX_TOTAL_FILES} files.`);
            return;
        }

        // Helper to get PDF page count
        async function getPdfPageCount(file) {
            const arrayBuffer = await file.arrayBuffer();
            const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
            return pdf.numPages;
        }

        // Analyze all PDFs (selected + new)
        const allPdfFiles = [...selectedFiles, ...newFiles].filter(f => f.type === "application/pdf");
        const pageCounts = await Promise.all(allPdfFiles.map(getPdfPageCount));
        const hasThreePagePdf = pageCounts.some(count => count === 3);

        if (hasThreePagePdf && (selectedFiles.length + newFiles.length > 1)) {
            showError("You cannot upload a 3-page PDF together with other files.");
            return;
        }

        // Validate new files (duplicates, size, blur)
        let validFiles = [];
        let blurCheckPromises = [];

        newFiles.forEach(file => {
            if (selectedFileNames.has(file.name)) {
                showError(`Duplicate file: ${file.name} has already been added.`);
                return;
            }

            if (file.size > MAX_FILE_SIZE_MB * 1024 * 1024) {
                showError(`File too large: ${file.name} exceeds ${MAX_FILE_SIZE_MB}MB.`);
                return;
            }

            let blurCheckPromise = new Promise((resolve) => {
                checkFileForBlurriness(file, function(errorMessage, hasError) {
                    if (hasError) {
                        showError(errorMessage);
                        resolve(null);
                    } else {
                        validFiles.push(file);
                        selectedFiles.push(file);
                        selectedFileNames.add(file.name);
                        displayFilePreview(file, previewContainer);
                        resolve(file);
                    }
                });
            });

            blurCheckPromises.push(blurCheckPromise);
        });

        await Promise.all(blurCheckPromises);

        if (validFiles.length > 0) {
            checkVisaFileForChatGPT(selectedFiles, processVisaData, function(error) {
                showError(error || "Failed to process files.");
            });
        }
    }



    // Process ChatGPT response
    function processVisaData(visaData) {
        if (visaData.visaDocument && Array.isArray(visaData.visaDocument)) {
            // Update documents array based on ChatGPT response
            visaData.visaDocument.forEach(doc => {
                let existingDoc = documents.find(d => d.code === doc.docType);
                if (existingDoc) {
                    existingDoc.uploaded = doc.valid;
                    existingDoc.required = doc.required === true;
                } else {
                    documents.push({
                        code: doc.docType,
                        name: '',
                        uploaded: doc.valid,
                        required: doc.required === true
                    });
                }
            });

            // Ensure names are populated
            loadRequiredDocuments($("#visaType").val(), function() {
                renderDocumentList();
                $("#uploadedDocs").val(serializeExtractedData());

                let allValid = checkAllRequiredDocumentsValid(visaData.visaDocument);
                let missingDocs = getMissingDocuments();
                if (missingDocs.length > 0) {
                    showError("Missing required documents: " + missingDocs.join(", ") + ".");
                } else if (!allValid) {
                    showError("Some uploaded documents are invalid.");
                }

                if (allValid) {
                    showPreviewSection();
                    previewPage(visaData);
                } else {
                    hidePreviewSection();
                }
                $("#saveDraftBtn").toggle(selectedFiles.length > 0);
            });
        } else {
            resetDocumentList();
            hidePreviewSection();
            $("#saveDraftBtn").hide();
            let missingDocs = getMissingDocuments();
            if (missingDocs.length > 0) {
                showError("No valid documents detected. Required: " + missingDocs.join(", ") + ".");
            }
        }
    }

    // Render document list
    function renderDocumentList() {
        let container = $("#documentList");
        container.empty();

        if (documents.length === 0) {
            loadRequiredDocuments($("#visaType").val(), function() {
                container.empty();
                documents.forEach(doc => appendDocumentItem(doc, container));
            });
        } else {
            documents.forEach(doc => appendDocumentItem(doc, container));
        }
    }

    function appendDocumentItem(doc, container) {
        let statusClass = doc.uploaded ? "uploaded" : "not-uploaded";
        let visibilityClass = doc.required ? "" : "hidden";
        let displayName = doc.name || doc.code; // Fallback to code if name is empty
        if (!doc.name) {
            console.warn(`No full name found for document code: ${doc.code}`);
        }
        let item = `
        <div class="document-item ${visibilityClass}" data-document-name="${doc.code}">
            <span class="document-icon ${statusClass}"></span>
            <span>${displayName}</span>
        </div>`;
        container.append(item);
    }

    // Display file preview
    function displayFilePreview(file, previewContainer) {
        let fileWrapper = $("<div>").addClass("image-preview").attr("data-filename", file.name);
        let closeButton = $("<span>").text("×").addClass("close-btn");

        if (file.type.startsWith("image/")) {
            let reader = new FileReader();
            reader.onload = function (e) {
                let img = $("<img>").attr("src", e.target.result).addClass("preview-img");
                img.on("click", function () {
                    $("#modalImage").attr("src", e.target.result);
                    $("#imageModal").fadeIn();
                });
                fileWrapper.append(closeButton).append(img);
                previewContainer.append(fileWrapper);
            };
            reader.readAsDataURL(file);
        } else if (file.type === "application/pdf") {
            renderPdfPreview(file, fileWrapper);
            previewContainer.append(fileWrapper);
        }
    }

    // Get missing documents
    function getMissingDocuments() {
        return documents.filter(doc => doc.required && !doc.uploaded).map(doc => doc.name);
    }

    // Check file blurriness
    function checkFileForBlurriness(file, callback) {
        let formData = new FormData();
        formData.append("files", file);
        formData.append("visaType", $("#visaType").val());

        showOverlayLoading();
        $.ajax({
            url: "/[[${systemContextRoot}]]/uploadVisa/checkFile?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
            type: "POST",
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                closeOverlayLoading();
                callback(response.error || "File checked successfully.", !!response.error);
            },
            error: function(xhr, status, error) {
                closeOverlayLoading();

                let errorMessage = "Error checking file: " + error;
                try {
                    // If response is JSON, parse it and extract the error message
                    let json = JSON.parse(xhr.responseText);
                    if (json.error) {
                        errorMessage = json.error;
                    }
                } catch (e) {
                    // Fallback in case it's not JSON
                    console.warn("Non-JSON error response", xhr.responseText);
                }

                callback(errorMessage, true);
            }
        });
    }

    // Process file with ChatGPT
    function checkVisaFileForChatGPT(files, callback, onError) {
        let formData = new FormData();
        files.forEach(file => formData.append("files", file));
        formData.append("visaType", $("#visaType").val());
        formData.append("isMainlandStudent", $("#isMainlandStudent").val());
        formData.append("residence", $("#residence").val());

        showOverlayLoading();
        $.ajax({
            url: "/[[${systemContextRoot}]]/uploadVisa/checkVisaFileForChatGPT?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
            type: "POST",
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                closeOverlayLoading();
                if (response.startsWith("Error")) {
                    if (onError) onError(response);
                    return;
                }
                let visaData = JSON.parse(response);
                callback(visaData);
            },
            error: function(xhr, status, error) {
                closeOverlayLoading();
                if (onError) onError("Failed to process file(s): " + error);
            }
        });
    }

    // Update document icon
    function updateDocumentIcon(documentCode, uploaded) {
        let $item = $(`.document-item[data-document-name="${documentCode}"]`);
        if ($item.length) {
            $item.find('.document-icon')
                .removeClass("uploaded not-uploaded")
                .addClass(uploaded ? "uploaded" : "not-uploaded");
        }
    }

    // Preview page
    function previewPage(visaData) {
        let formattedEndDate = formatDateForInput(visaData.visaEndDate);
        $("#visaEndDate").val(formattedEndDate);
        $("#visaNumber").val(visaData.visaNumber);
        $("#studentName").val(visaData.studentName || "");
        $("#institutionName").val(visaData.institutionName || "");
        $("#previewSection").fadeIn();
        $("#submitBtn").fadeIn();
    }

    // Format date
    function formatDateForInput(dateString) {
        let parts = dateString.split('-');
        if (parts.length === 3) {
            let day = parts[0];
            let month = parts[1];
            let year = parts[2];
            return `${year}-${month}-${day}`;
        }
        return "";
    }

    // Load visa types
    function loadVisaTypes(callback) {
        showOverlayLoading();
        $.ajax({
            url: "/[[${systemContextRoot}]]/uploadVisa/getVisaTypes",
            type: "GET",
            dataType: "json",
            success: function (response) {
                closeOverlayLoading();
                var visaDropdown = $("#visaType");
                visaDropdown.empty();
                visaDropdown.append('<option value="">Please select</option>');

                if (response && response.length > 0) {
                    $.each(response, function (index, item) {
                        let visaDocNameChi = item.VISATYPENAMECHI ? ` (${item.VISATYPENAMECHI})` : '';
                        visaDropdown.append(`<option value="${item.VISATYPECODE}">${item.VISATYPEDESC}${visaDocNameChi}</option>`);
                    });


                    let existingVisaCode = "[[${visaUploadInfo?.vtypeCode}]]";
                    if (existingVisaCode) {
                        visaDropdown.val(existingVisaCode);
                        loadRequiredDocuments(existingVisaCode, callback);
                    } else if (callback) {
                        callback();
                    }
                }
            },
            error: function () {
                closeOverlayLoading();
                console.error("Error fetching visa types");
                if (callback) callback();
            }
        });

        $("#visaType").on("change", function() {
            selectedFiles = [];
            selectedFileNames.clear();
            deletedFilesSeqNo = [];
            $("#previewContainer").empty();
            hidePreviewSection();
            $("#saveDraftBtn").hide();
            documents = [];
            loadRequiredDocuments($(this).val());
        });
    }

    // Load required documents
    function loadRequiredDocuments(visaType, callback) {
        if (!visaType) {
            $("#documentList").empty();
            if (callback) callback();
            return;
        }

        showOverlayLoading();
        $.ajax({
            url: "/[[${systemContextRoot}]]/uploadVisa/getVisaDocuments",
            type: "GET",
            data: { visaType: visaType },
            dataType: "json",
            success: function(response) {
                closeOverlayLoading();
                let isMainlandStudent = $("#isMainlandStudent").val();
                let residence = $("#residence").val();

                // Create a map of docType to docTypeName for all documents (unfiltered)
                let docNameMap = new Map(response.map(doc => [doc.docType, doc.docTypeName]));

                // Apply filtering for new documents based on conditions
                let filteredDocs = response.filter(doc => {
                    if (doc.conditionType === "ORIGIN_MAINLAND") {
                        return isMainlandStudent === doc.conditionValue;
                    }
                    if (doc.conditionType === "RESIDENCE") {
                        return residence === doc.conditionValue;
                    }
                    return true;
                });

                // Merge existing documents with filteredDocs, preserving ChatGPT's required/uploaded status
                let newDocuments = [];
                documents.forEach(doc => {
                    // Preserve existing document with its required/uploaded status
                    let updatedDoc = {
                        code: doc.code,
                        name: docNameMap.get(doc.code) || doc.name || doc.code, // Use DB name, existing name, or code as fallback
                        uploaded: doc.uploaded,
                        required: doc.required // Preserve ChatGPT's required status
                    };
                    newDocuments.push(updatedDoc);
                });

                // Add new documents from filteredDocs that aren't in the current documents array
                filteredDocs.forEach(doc => {
                    if (!newDocuments.find(d => d.code === doc.docType)) {
                        newDocuments.push({
                            code: doc.docType,
                            name: doc.docTypeName,
                            uploaded: false,
                            required: doc.required === "Y" // Use DB required status for new documents
                        });
                    }
                });

                // Update documents array
                documents = newDocuments;
                renderDocumentList();
                $("#uploadedDocs").val(serializeExtractedData());
                if (callback) callback();
            },
            error: function(xhr, status, error) {
                closeOverlayLoading();
                $("#documentList").html("<p>Error loading document requirements</p>");
                if (callback) callback();
            }
        });
    }

    // Save as draft
    function saveAsDraft(isSubmit) {
        let studentName = $("#studentName").val();
        let institutionName = $("#institutionName").val();
        let visaCode = $("#visaType").val();
        let visaEndDate = $("#visaEndDate").val();
        let visaNumber = $("#visaNumber").val();
        let pidm = $("input[name='pidm']").val();
        let seqNo = $("input[name='seqNo']").val();

        if (!visaCode) {
            showError("Please select the visa type.");
            return;
        }

        let formdata = new FormData();
        formdata.append("pidm", pidm);
        formdata.append("seqNo", seqNo);
        formdata.append("studentName", studentName);
        formdata.append("institutionName", institutionName);
        formdata.append("visaCode", visaCode);
        formdata.append("visaNumber", visaNumber || "");
        formdata.append("visaEndDate", visaEndDate || "");
        formdata.append("uploadedDocs", serializeExtractedData());

        if (selectedFiles && selectedFiles.length > 0) {
            for (let file of selectedFiles) {
                formdata.append("files", file);
            }
        }

        showOverlayLoading();
        $.ajax({
            url: "/[[${systemContextRoot}]]/uploadVisa/save?[[${_csrf.parameterName}]]=[[${_csrf.token}]]",
            type: "POST",
            data: formdata,
            contentType: false,
            processData: false,
            success: function(response) {
                if (isSubmit) {
                    window.location.href = "/[[${systemContextRoot}]]/uploadVisa/confirmVisa";
                } else {
                    closeOverlayLoading();
                    Swal.fire({
                        title: "Success!",
                        html: `${response.replace(/</g, "&lt;").replace(/>/g, "&gt;")}<br><b>Note:</b> Your data has been saved as a draft but not submitted. Please click 'Submit and Save' to complete the submission.`,icon: "success",
                        confirmButtonText: "OK"
                    }).then(() => {
                        window.location.reload();
                    });
                }
            },
            error: function(xhr, status, error) {
                closeOverlayLoading();
                showError("Error saving draft: " + error);
            }
        });
    }

    // Helper functions
    function base64ToBlob(base64, mime) {
        if (!isValidBase64(base64)) {
            return null;
        }
        try {
            const byteCharacters = atob(base64);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            return new Blob([new Uint8Array(byteNumbers)], { type: mime });
        } catch (error) {
            console.error("Error decoding Base64:", error);
            return null;
        }
    }

    function isValidBase64(str) {
        if (!str || str.trim() === "") return false;
        // Check if string contains only valid Base64 characters
        const base64Regex = /^[A-Za-z0-9+/=]+$/;
        if (!base64Regex.test(str)) return false;
        // Optionally, verify it can be decoded
        try {
            atob(str);
            return true;
        } catch (e) {
            return false;
        }
    }

    function checkAllRequiredDocumentsValid(visaDocuments) {
        return documents.every(doc => !doc.required || doc.uploaded);
    }

    function resetPreviewSection() {
        $("#previewSection").fadeOut();
        $("#visaEndDate").val("");
        $("#visaNumber").val("");
        $("#studentName").val("");
        $("#institutionName").val("");
        $("#dropZone").show();
        $("#fileInput").val("");
        $("#submitBtn").fadeOut();
    }

    function showPreviewSection() {
        $("#previewSection").fadeIn();
        $("#submitBtn").fadeIn();
    }

    function hidePreviewSection() {
        $("#visaEndDate").val("");
        $("#visaNumber").val("");
        $("#studentName").val("");
        $("#institutionName").val("");
        $("#previewSection").fadeOut();
        $("#submitBtn").fadeOut();
    }

    function resetDocumentList() {
        documents = [];
        $("#documentList").empty();
        let visaType = $("#visaType").val();
        if (visaType) {
            loadRequiredDocuments(visaType);
        }
    }
</script>
</body>
</html>