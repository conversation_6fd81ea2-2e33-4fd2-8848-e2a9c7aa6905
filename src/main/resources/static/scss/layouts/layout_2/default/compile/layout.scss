/* ------------------------------------------------------------------------------
 *
 *  # Core layout
 *
 *  Content area, sidebar, page header and boxed layout styles
 *
 * ---------------------------------------------------------------------------- */

// Import custom template config
@import "../../../../config";
@import "../../../../shared/utils/ll-functions";
@import "../../../../shared/utils/ll-mixins";
@import "../../../../themes/default/colors/palette";

// Core variables and mixins
@import "../../../../_bootstrap/functions";
@import "../../../../_bootstrap/variables";
@import "../../../../_bootstrap/mixins";

// Import template's variables
@import "../variables/variables-core";
@import "../variables/variables-custom";
@import "../../../../themes/default/bootstrap_limitless/mixins";

// Content
@import "../layout/content";

// Header
@import "../layout/header";

// Sidebar
@import "../layout/sidebar";

// Boxed layout
@import "../layout/boxed";
