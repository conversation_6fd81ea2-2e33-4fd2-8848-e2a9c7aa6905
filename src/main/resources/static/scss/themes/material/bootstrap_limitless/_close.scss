/* ------------------------------------------------------------------------------
 *
 *  # Close button
 *
 *  Overrides of default close button styles
 *
 * ---------------------------------------------------------------------------- */

// Base
.close {
    opacity: .75;
    @include transition(color ease-in-out $component-transition-timer, opacity ease-in-out $component-transition-timer);

    // Hover and focus states
    @include hover-focus {
        outline: 0;
    }

    // Hover and focus states
    &:not(:disabled):not(.disabled) {
	    @include hover-focus {
	        opacity: 1;
	    }
    }
}
