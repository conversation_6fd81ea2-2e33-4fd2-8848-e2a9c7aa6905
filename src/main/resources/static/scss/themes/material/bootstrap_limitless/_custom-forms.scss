/* ------------------------------------------------------------------------------
 *
 *  # Custom form inputs
 *
 *  Custom checkboxes, radios, selects and file inputs
 *
 * ---------------------------------------------------------------------------- */


//
// Checkboxes and radios
//

// Container
.custom-control {
    margin-bottom: $form-check-margin-y;

    // Last item doesn't have spacing
    &:last-child {
        margin-bottom: 0;
    }
}

// Inline list
.custom-control-inline {
    margin-bottom: 0;

    // Last item doesn't have spacing
    &:last-child {
        margin-right: 0;
    }
}

// Input
.custom-control-input {

    // Active state
    &:active ~ .custom-control-label::before {
        @include transition(all ease-in-out $component-transition-timer);
    }

    // Disabled state
    &:disabled {
        ~ .custom-control-label {
            cursor: $cursor-disabled;
        }
    }
}

// Custom control indicators
.custom-control-label {
    cursor: pointer;

    // Background
    &::before {
        top: (($line-height-computed - $custom-control-indicator-size) / 2);
    }

    // Foreground (icon)
    &::after {
        top: (($line-height-computed - $custom-control-indicator-size) / 2);
    }
}

// Right input alignment
.custom-control-right {
    padding-left: 0;
    padding-right: ($custom-control-gutter + $custom-control-indicator-size);

    // Input
    .custom-control-input {
        right: 0;
    }

    // Label
    .custom-control-label {
        &:before,
        &:after {
            left: auto;
            right: 0;
        }
    }
}


//
// Select
//

.custom-select {
    cursor: pointer;
    border-width: $custom-select-border-width 0;
    border-top-color: transparent;
    @include transition(all ease-in-out $component-transition-timer);

    // But add shadow highlight
    &:focus {
        box-shadow: $custom-select-focus-box-shadow;
    }

    // Disabled state
    &:disabled {
        cursor: $cursor-disabled;
        border-bottom-style: dashed;

        // Hover state
        @include hover-focus {
            box-shadow: none;
        }
    }
}


//
// File
//

.custom-file-label {
    border-width: $custom-file-border-width 0;
    border-top-color: transparent;
    margin-bottom: 0;

    // "Browse" title
    &:after {
        border-left: 0;
        font-weight: $btn-font-weight;
        text-transform: uppercase;
    }
}
