/* ------------------------------------------------------------------------------
 *
 *  # Card component
 *
 *  Overrides for card bootstrap component
 *
 * ---------------------------------------------------------------------------- */


//
// Base styles
//

// Card base
.card {
    margin-bottom: $spacer;
    box-shadow: $card-box-shadow;
    border-width: 0;
 
    // Full screen mode
    &.fixed-top {
        overflow: auto;
        max-height: 100%;
    }
}

// Card body
.card-body:not(.card) {
    + .card-body:not(.card) {
        border-top: $card-border-width solid $card-border-color;
    }
}

// Card title
.card-title {
    position: relative;
}

// Card header
.card-header {

    // Top border radius fix
    .card:not([class*=border-]) & {
        @include border-top-radius($card-border-radius);
    }

    // Title
    .card-title {
        margin-bottom: 0;
    }

    // Inside dark card
    .card[class*=bg-]:not(.bg-light):not(.bg-white):not(.bg-transparent) & {
        border-bottom-color: rgba($white, 0.1);
    }

    // If header has no bottom border,
    // make vertical spacing bigger
    &:not([class*=bg-]):not([class*=alpha-]) {
        background-color: transparent;
        padding-top: $card-spacer-x;
        padding-bottom: $card-spacer-x;
        border-bottom-width: 0;

        // Remove top padding from card body
        + .card-body,
        + * > .card-body:first-child {
            padding-top: 0;
        }
    }
}

// Card footer
.card-footer {

    // Add top borders if comes last
    &:first-child,
    .card-header + &,
    .card-header + * > &:first-child {
        border-top: 0;
        border-bottom: $card-border-width solid $card-border-color;
    }

    // Add border if not last
    &.border-bottom-1 {
        border-bottom: $card-border-width solid $card-border-color;
    }
}


//
// Header navs
//

.card-header-tabs {
    margin-bottom: 0;

    // Link
    .nav-link {
        border-top-width: 0;
    }
}


//
// Image controls
//

// Card image
.card-img {

    // Inside card body
    .card-body & {
        @include border-radius($card-border-radius);
    }
}

// Img actions
.card-img-actions {
    position: relative;

    // Hover state
    @include hover-focus {
        .card-img-actions-overlay {
            opacity: 1;
            visibility: visible;
        }
    }
}

// Img actions overlay
.card-img-actions-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba($black, 0.75);
    color: $white;
    opacity: 0;
    visibility: hidden;
    @include transition(all ease-in-out $component-transition-timer);
}


//
// Card actions
//

// Link base
[data-action] {
    cursor: pointer;

    // Icons base
    &:after {
        font-family: $icon-font-family;
        font-size: $icon-font-size;
        min-width: $icon-font-size;
        text-align: center;
        display: inline-block;
        vertical-align: middle;
        @include ll-font-smoothing();
    }
}

// Collapse icon
[data-action="collapse"]:after {
    content: $icon-card-action-collapse;
}

// Reload icon
[data-action="reload"]:after {
    content: $icon-card-action-reload;
}

// Remove icon
[data-action="remove"]:after {
    content: $icon-card-action-remove;
    font-size: $font-size-base;
}

// Move icon
[data-action="move"] {
    cursor: default;

    // Icon
    &:after {
        content: $icon-card-action-move;
    }
}

// Fullscreen
[data-action="fullscreen"] {
    &:after {
        content: $icon-card-action-full-base;
    }

    // Change icon when in fullscreen mode
    &[data-fullscreen="active"]:after {
        content: $icon-card-action-full-active;
    }
}

// Open modal icon
[data-action="modal"]:after {
    content: $icon-card-action-modal;
}


//
// Collapsable panels (aka, accordion)
//

.card-group-control {
    .card-title > a {
        display: inline-block;

        // Collapsible icon
        &:before {
            content: $icon-card-expand;
            font-family: $icon-font-family;
            position: absolute;
            top: 50%;
            margin-top: -($icon-font-size / 2);
            font-size: $icon-font-size;
            font-weight: $font-weight-normal;
            line-height: 1;
            @include ll-font-smoothing();
        }

        // Change icon if collapsed
        &.collapsed:before {
            content: $icon-card-collapse;
        }
    }

    // Left control position
    &-left {
        .card-title > a {
            padding-left: ($icon-font-size + $element-spacer-x);

            // Icon alignment
            &:before {
                left: 0;
            }
        }
    }

    // Right control position
    &-right {
        .card-title > a {
            padding-right: ($icon-font-size + $element-spacer-x);

            // Re-align the icon
            &:before {
                right: 0;
            }
        }
    }
}


//
// Card group
//

.card-group {
    box-shadow: $card-box-shadow;
    @include border-bottom-radius($card-border-radius);

    .card:not(:last-child) {
        border-right-width: $card-border-width;
    }
}
