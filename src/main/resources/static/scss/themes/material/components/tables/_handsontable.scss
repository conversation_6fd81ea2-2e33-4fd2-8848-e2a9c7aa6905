/* ------------------------------------------------------------------------------
 *
 *  # Handsontable
 *
 *  Styles for handsontable.min.js - JavaScript Spreadsheet library
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-handsontable {

    /*rtl:begin:ignore*/

    //
    // Base
    //

    // Container
    .hot-container {
        position: relative;
        box-shadow: 0 0 0 $table-border-width $table-border-color;
        overflow: hidden;
        text-align: left;

        // Scrolling
        &.hot-scrollable {
            height: $hot-scrollable-max-height;
        }

        // If no scrolling, stretch holder vertically
        &:not(.hot-scrollable) > .handsontable > .ht_master > .wtHolder {
            height: 100%!important;
        }

        // Mobile view
        @include media-breakpoint-down(sm) {
            overflow-x: hidden;
            overflow-y: auto;

            // Remove holder strtching
            .ht_master .wtHolder {
                max-width: 100%;
                min-width: auto;
            }

            // If no scrolling, stretch holder vertically
            &:not(.hot-scrollable) .wtHolder {
                height: 100%!important;
            }
        }
    }

    // Core
    .handsontable {
        position: relative;
        direction: ltr;

        // Hide helper
        .hide {
            display: none;
        }

        // Relative position helper
        .relative {
            position: relative;
        }

        // Auto size
        &.htAutoSize {
            visibility: hidden;
            left: -99000px;
            position: absolute;
            top: -99000px;
        }

        // Hider
        .wtHider {
            position: relative;
            width: 0;
        }

        // Spreader
        .wtSpreader {
            position: relative;
            width: 0;
            height: auto;
        }

        // Read only cells
        .htDimmed {
            color: $gray-600;
        }
    }

    // Table core
    .handsontable {

        // Element's box sizing
        table,
        tbody,
        thead,
        td,
        th,
        input,
        textarea,
        div {
            box-sizing: content-box;
        }

        // Inputs
        input,
        textarea {
            min-height: initial;
        }

        // Core table
        table.htCore {
            border-collapse: separate;
            border-spacing: 0;
            margin: 0;
            border-width: 0;
            table-layout: fixed;
            width: 0;
            outline-width: 0;
            max-width: none;
            max-height: none;
        }

        // Column
        col {
            &,
            &.rowHeader {
                width: $hot-row-header-width;
            }
        }
    }


    // Table elements
    // ------------------------------

    .handsontable {

        //
        // Table cells base
        //

        // Common styles
        th,
        td {
            position: relative;
            border-top-width: 0;
            border-left-width: 0;
            border-right: $table-border-width solid $table-border-color;
            border-bottom: $table-border-width solid $table-border-color;
            height: $hot-cell-height;
            empty-cells: show;
            padding: 0 $hot-cell-padding-x;
            overflow: hidden;
            outline-width: 0;
            white-space: pre-line;
        }

        // Content cells
        td {

            // Default background color
            &:not([class*=bg-]) {
                background-color: $hot-cell-bg;
            }

            // Invalid state
            &.htInvalid {
                background-color: $color-danger-50 !important; // gives priority over td.area selection background
            }

            // Prevent wrapping
            &.htNoWrap {
                white-space: nowrap;
            }

            // Active cell
            &.active {
                background-color: $table-active-bg;
            }

            // Highlighted area
            &.area {
                background-color: $hot-cell-highlight-bg;
            }
        }

        // Table headers
        th {
            text-align: center;
            font-weight: normal;
            white-space: nowrap;

            // Default background color
            &:not([class*=bg-]) {
                background-color: $hot-header-bg;
            }
        }

        // Control paddings in table head
        thead th {
            padding: 0;

            // Text
            .relative {
                padding: 0 $hot-cell-padding-x;
            }
        }


        //
        // Selection
        //

        // When cell is selected
        tbody th.ht__highlight,
        thead th.ht__highlight {
            background-color: darken($hot-header-bg, 2.5%);
        }

        // When column is selected
        &.ht__selection--columns thead th.ht__highlight,
        &.ht__selection--rows tbody th.ht__highlight {
            background-color: $hot-cell-highlight-bg;
        }


        //
        // Borders
        //

        // If no frame
        tr:first-child th.htNoFrame,
        th:first-child.htNoFrame,
        th.htNoFrame {
            border-left-width: 0;
            background-color: $hot-cell-bg;
            border-color: $hot-cell-bg;
        }

        // Add left border to first cells
        th:first-child,
        th:nth-child(2),
        td:first-of-type,
        .htNoFrame + th,
        .htNoFrame + td {
            border-left: $table-border-width solid $table-border-color;
        }

        // Add left border to headers starting from second
        &.htRowHeaders thead tr th:nth-child(2) {
            border-left: $table-border-width solid $table-border-color;
        }

        // Cells in first row have top border
        tr:first-child {
            th,
            td {
                border-top: $table-border-width solid $table-border-color;
            }
        }
    }


    // Borders control
    // ------------------------------

    // Remove double borders in scrollable master table
    .ht_master {

        // Left borders
        &:not(.innerBorderLeft):not(.emptyColumns) {
            ~ .handsontable tbody tr th,
            ~ .handsontable:not(.ht_clone_top) thead tr th:first-child {
                border-right-width: 0;
            }
        }

        // Bottom borders
        &:not(.innerBorderTop) {
            thead tr:last-child th,
            thead tr.lastChild th,
            ~ .handsontable thead tr:last-child th,
            ~ .handsontable thead tr.lastChild th {
                border-bottom-width: 0;
            }
        }

        .htRowHeaders &.innerBorderLeft {
            ~ .ht_clone_top_left_corner th:nth-child(2),
            ~ .ht_clone_left td:first-of-type {
                border-left-width: 0;
            }
        }
    }


    //
    // Top border
    //

    // Top border in first cells
    .ht_master {
        tbody tr:first-child td {
            border-top-color: transparent;
        }
    }

    // Top cloned
    .ht_clone_top {
        tr:first-child th,
        tr:first-child td {
            border-top-color: transparent;
        }
    }

    // Left cloned
    .ht_clone_left,
    .ht_clone_top_left_corner {
        thead tr:first-child th,
        tbody tr:first-child td,
        tbody tr:first-child th {
            border-top-color: transparent;
        }
    }


    //
    // Left border
    //

    // Left border in first cells
    .handsontable {
        th:first-child,
        td:first-child {
            border-left-color: transparent;
        }
    }


    //
    // Right border
    //

    // Right border in right cells
    .ht_master td:last-child,
    .ht_clone_top th:last-child,
    .ht_clone_top td:last-child {
        border-right-color: transparent;
    }


    //
    // Bottom border
    //

    // Bottom border in last cells
    .ht_master,
    .ht_clone_left {
        tbody tr:last-child th,
        tbody tr:last-child td {
            border-bottom-color: transparent;
        }
    }


    //
    // Override top borders
    //

    // Top border if table has both row and column headers
    .htRowHeaders.htColumnHeaders {
        .ht_clone_top_left_corner,
        .ht_clone_left {
            tbody tr:first-child th {
                border-top-color: $table-border-color;
            }
        }
    }

    // Top border if only column header
    .htColumnHeaders .handsontable {
        tr:first-child td {
            border-top-color: $table-border-color;
        }
    }


    // Plugins
    // ------------------------------

    //
    // Column and row resizer
    //

    // Common styles
    .manualColumnResizer,
    .manualRowResizer {

        // Display if active
        &.active {
            display: block;
            z-index: 199;
        }

        // Apply background color
        &:hover,
        &.active {
            background-color: $table-active-bg;
        }
    }

    // Column resizer
    .manualColumnResizer {
        position: fixed;
        top: 0;
        cursor: col-resize;
        z-index: 110;
        width: 0.3125rem;
        height: $hot-cell-height;
    }

    // Row resizer
    .manualRowResizer {
        position: fixed;
        left: 0;
        cursor: row-resize;
        z-index: 110;
        height: 0.3125rem;
        width: ($hot-cell-height * 2);
    }

    // Column resizer guide
    .manualColumnResizerGuide {
        position: fixed;
        right: 0;
        top: 0;
        background-color: $table-active-bg;
        display: none;
        width: 0;
        border-right: $table-border-width dashed $table-border-color;
        margin-left: 0.3125rem;
    }

    // Row resizer guide
    .manualRowResizerGuide {
        position: fixed;
        left: 0;
        bottom: 0;
        background-color: $table-active-bg;
        display: none;
        height: 0;
        border-bottom: $table-border-width dashed $table-border-color;
        margin-top: 0.3125rem;
    }


    //
    // Sorting
    //

    .handsontable {

        // Cell with enabled sorting
        .columnSorting {
            position: relative;
            cursor: pointer;

            // Arrows
            &:before,
            &:after {
                font-family: $icon-font-family;
                font-size: $font-size-sm;
                position: absolute;
                display: inline-block;
                top: 50%;
                transform: translateY(-50%);
                right: -($icon-font-size + $element-spacer-x);
                line-height: 1;
                opacity: 0.5;
                @include ll-font-smoothing();
                @include transition(opacity ease-in-out $component-transition-timer);
            }
            &:before {
                content: $icon-table-sorting-down;
                margin-top: ($font-size-sm / 3);
            }
            &:after {
                content: $icon-table-sorting-up;
                margin-top: -($font-size-sm / 3);
            }
            &.ascending {
                &:before {
                    content: none;
                }

                &:after {
                    margin-top: 0;
                }
            }
            &.descending {
                &:before {
                    margin-top: 0;
                }

                &:after {
                    content: none;
                }
            }

            // Hover state
            @include hover {
                &:before,
                &:after {
                    opacity: 1;
                }
            }
        }
    }


    //
    // Border lines
    //

    // Width border
    .wtBorder {
        position: absolute;
        font-size: 0;

        // Hidden border
        &.hidden {
            display: none!important;
        }

        // Corners
        &.corner {
            font-size: 0;
            cursor: crosshair;
        }
    }

    // Height border
    .htBorder {
        &.htFillBorder {
            background-color: theme-color("primary");
            width: $table-border-width;
            height: $table-border-width;
        }
    }


    //
    // Inputs
    //

    // Input
    .handsontableInput {
        border: 0;
        outline-width: 0;
        margin: 0;
        padding: 0 ($hot-cell-padding-x + rem-calc($table-border-width));
        padding-top: rem-calc($table-border-width);
        font-family: inherit;
        height: $hot-cell-height;
        line-height: $hot-cell-height;
        font-size: inherit;
        resize: none;
        display: block;
        width: auto!important;
        background-color: $hot-cell-bg;
        box-shadow: 0 0 0 ($table-border-width * 2) $hot-cell-highlight-border-color inset;
    }

    // Input holder
    .handsontableInputHolder {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 100;
    }

    // Editor
    .htSelectEditor {
        -webkit-appearance: menulist-button !important;
        position: absolute;
        width: auto;
        outline: 0;
        @include border-radius(0);
    }


    //
    // Submenu
    //

    .htSubmenu {
        position: relative;

        // Arrow
        > div:after{
            content: $icon-menu-sub-arrow-ltr;
            font-family: $icon-font-family;
            position: absolute;
            top: 50%;
            margin-top: -($icon-font-size / 2);
            right: $dropdown-item-padding-x;
            font-size: $icon-font-size;
            line-height: 1;
            opacity: 0.8;
            @include ll-font-smoothing();
        }
    }


    //
    // Alignment classes
    //

    .handsontable {

        // Horizontal
        .htLeft {
            text-align: left;
        }
        .htCenter {
            text-align: center;
        }
        .htRight {
            text-align: right;
        }
        .htJustify {
            text-align: justify;
        }

        // Vertical
        .htTop {
            vertical-align: top;
        }
        .htMiddle {
            vertical-align: middle;
        }
        .htBottom {
            vertical-align: bottom;
        }
    }


    //
    // Autocomplete
    //

    // Placeholder
    .htPlaceholder {
        color: $input-placeholder-color;
    }

    // Arrow
    .htAutocompleteArrow {
        cursor: default;
        text-indent: -9999999px;
        line-height: 1;
        position: absolute;
        top: 50%;
        right: $caret-width;
        margin-top: -($caret-width / 2);
        display: inline-block;
        margin-right: $caret-width;
        border-top:   $caret-width dashed;
        border-right: $caret-width solid transparent;
        border-left:  $caret-width solid transparent;
        opacity: 0.75;
    }

    // Hover state
    .handsontable {
        td:hover,
        th:hover {
            .htAutocompleteArrow {
                opacity: 1;
            }
        }

        // When column is selected
        td.area .htAutocompleteArrow {
            color: $hot-cell-highlight-bg;
        }
    }


    //
    // Checkbox renderer
    //

    // Input
    .htCheckboxRendererInput {
        display: inline-block;
        vertical-align: middle;

        // Mute if no value
        &.noValue {
            opacity: 0.5;
        }
    }

    // Label
    .htCheckboxRendererLabel {
        cursor: pointer;
        display: inline-block;
        width: 100%;
        margin-bottom: 0;

        // Add right spacing
        > .htCheckboxRendererInput {
            margin-right: $element-spacer-x;
        }
    }


    //
    // Tweening
    //

    .handsontable {

        // Hide
        &.hide-tween {
            animation: opacity-hide 0.3s;
            animation-fill-mode: forwards;
        }

        // Show
        &.show-tween {
            animation: opacity-show 0.3s;
            animation-fill-mode: forwards;
        }
    }

    // Animation
    @-webkit-keyframes opacity-hide {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
    @keyframes opacity-hide {
        from {
            opacity: 1;
        }
        to {
            opacity: 0;
        }
    }
    @-webkit-keyframes opacity-show {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }
    @keyframes opacity-show {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }


    //
    // Dropdown menu
    //

    .handsontable {

        // Base
        &.listbox {
            margin: 0;

            // Table
            .ht_master table {
                border-collapse: separate;
                border: $table-border-width solid $table-border-color;
                background-color: $hot-cell-bg;
            }

            // Remove borders
            th,
            tr:first-child th,
            tr:last-child th,
            tr:first-child td,
            td {
                border: 0;
            }

            // Cell and row transition
            tr,
            th,
            td {
                @include transition(all ease-in-out $component-transition-timer);
            }

            // Prevent wrapping
            th,
            td {
                white-space: nowrap;
                text-overflow: ellipsis;
            }

            // Read only cell
            td.htDimmed {
                cursor: pointer;
                color: inherit;
                font-style: inherit;
            }

            // Hide additional borders
            .wtBorder {
                visibility: hidden;
            }

            // Active and hover states
            tr td.current,
            tr:hover td {
                background-color: $dropdown-link-hover-bg;
            }
        }
    }


    //
    // Change cell type dropdown
    //

    // Header button
    .changeType {
        border: 0;
        background-color: transparent;
        padding: 0;
        float: right;
        margin-right: $hot-cell-padding-x;
        line-height: $icon-font-size;
        margin-top: ($line-height-computed - $icon-font-size) / 2;
        cursor: pointer;
        opacity: 0.75;

        // Hover state
        @include hover {
            opacity: 1;
        }
    }

    // Dropdown
    .changeTypeMenu {
        position: absolute;
        border: $dropdown-border-width solid $dropdown-border-color;
        background-color: $dropdown-bg;
        padding: $dropdown-padding-y 0;
        display: none;
        width: $dropdown-min-width;
        z-index: $zindex-dropdown;

        // Menu items
        li {
            list-style: none;
            padding: $dropdown-item-padding-y $dropdown-item-padding-x;
            padding-right: (($dropdown-item-padding-x + ($dropdown-item-padding-x / 2)) + $icon-font-size);
            cursor: pointer;
            position: relative;
            @include transition(all ease-in-out $component-transition-timer);

            // Hover state
            @include hover {
                background-color: $dropdown-link-hover-bg;
            }

            // Add checkmark icon to the active type
            &.active:after {
                content: $icon-checkbox-tick;
                font-family: $icon-font-family;
                position: absolute;
                right: $dropdown-item-padding-x;
                top: 50%;
                margin-top: -($icon-font-size / 2);
                display: inline-block;
                font-size: $icon-font-size;
                line-height: 1;
                @include ll-font-smoothing();
            }
        }
    }


    //
    // Cloned tables appearance
    //

    // Top
    .ht_clone_top {
        z-index: 101;
    }

    // Left
    .ht_clone_left {
        z-index: 102;
    }

    // Top left and bottom left
    .ht_clone_top_left_corner,
    .ht_clone_bottom_left_corner {
        z-index: 103;
    }

    // Debug
    .ht_clone_debug {
        z-index: 103;
    }


    //
    // Search results
    //

    .handsontable {
        td.htSearchResult {
            background-color: $hot-search-result-bg;
        }
    }


    //
    // Cell borders
    //

    // Base
    .htBordered {
        border-width: $table-border-width;

        // Top
        &.htTopBorderSolid {
            border-top-style: solid;
            border-top-color: $table-border-color;
        }

        // Right
        &.htRightBorderSolid {
            border-right-style: solid;
            border-right-color: $table-border-color;
        }

        // Bottom
        &.htBottomBorderSolid {
            border-bottom-style: solid;
            border-bottom-color: $table-border-color;
        }

        // Left
        &.htLeftBorderSolid {
            border-left-style: solid;
            border-left-color: $table-border-color;
        }
    }

    // Group indicator
    .handsontable thead tr:nth-last-child(2) th.htGroupIndicatorContainer {
        border-bottom: $table-border-width solid $table-border-color;
        padding-bottom: $hot-cell-padding-y;
    }

    // Right borders
    .handsontable tbody tr th:nth-last-child(2),
    .ht_clone_top_left_corner thead tr th:nth-last-child(2) {
        border-right: $table-border-width solid $table-border-color;
    }


    //
    // Collapsible button
    //

    // Collapse button
    .htCollapseButton {
        line-height: 0.625rem;
        text-align: center;
        border: 0;
        cursor: pointer;
        margin-bottom: 0.1875rem;
        position: relative;
        @include size(0.625rem);
        @include border-radius($border-radius);

        &:after {
            content: "";
            height: 300%;
            width: $table-border-width;
            display: block;
            background-color: #ccc;
            margin-left: 0.25rem;
            position: absolute;
            bottom: 0.625rem;
        }

        // Inside thead
        thead & {
            position: absolute;
            top: 0.3125rem;
            right: 0.3125rem;
            background-color: #fff;

            &:after {
                height: $table-border-width;
                width: 700%;
                right: 0.625rem;
                top: 0.25rem;
            }
        }
    }

    // Expand button
    .handsontable {

        // Inside heading
        th .htExpandButton {
            position: absolute;
            line-height: 0.625rem;
            text-align: center;
            border: 0;
            cursor: pointer;
            top: 0;
            display: none;
            @include size(0.625rem);
            @include border-radius($border-radius);

            // Clickable
            &.clickable {
                display: block;
            }
        }

        // Inside table head
        thead th .htExpandButton {
            top: 0.3125rem;
        }
    }

    // Collapsible indicator
    .collapsibleIndicator {
        position: absolute;
        top: 50%;
        transform: translate(0%, -50%);
        right: 0.3125rem;
        border: 0;
        line-height: 0.625rem;
        color: $body-color;
        font-size: $font-size-base;
        cursor: pointer;
        background-color: $gray-300;
        @include size(0.625rem);
        @include border-radius($border-radius-round);
    }


    //
    // Hidden elements
    //

    .handsontable {

        // Hidden column
        col.hidden {
            width: 0!important;
        }

        // Lighten border
        table tr th.lightRightBorder {
            border-right: $table-border-width solid lighten($table-border-color, 15%);
        }

        // Hidden row
        tr.hidden {
            &,
            td,
            th {
                display: none;
            }
        }
    }


    //
    // Cloned tables overflows
    //

    // Common
    .ht_master,
    .ht_clone_left,
    .ht_clone_top,
    .ht_clone_bottom {
      overflow: hidden;
    }

    // Holder
    .wtHolder {

        // Master
        .ht_master & {
            overflow: auto;
        }

        // Left
        .ht_clone_left & {
            overflow-x: hidden;
            overflow-y: auto;
        }

        // Top and bottom
        .ht_clone_top &,
        .ht_clone_bottom & {
            overflow-x: hidden;
            overflow-y: hidden;
        }
    }


    //
    // Walkontable debug overlay
    //

    // Hidden
    .wtDebugHidden {
        display: none;
    }

    // Visible
    .wtDebugVisible {
        display: block;
        animation-duration: 0.5s;
        animation-name: wtFadeInFromNone;
    }

    // Animation
    @keyframes wtFadeInFromNone {
        0% {
            display: none;
            opacity: 0;
        }

        1% {
            display: block;
            opacity: 0;
        }

        100% {
            display: block;
            opacity: 1;
        }
    }
    @-webkit-keyframes wtFadeInFromNone {
        0% {
            display: none;
            opacity: 0;
        }

        1% {
            display: block;
            opacity: 0;
        }

        100% {
            display: block;
            opacity: 1;
        }
    }


    //
    // Mobile text editor
    //

    // Base
    .handsontable {
        &.mobile {
            &,
            .wtHolder {
                -webkit-touch-callout: none;
                -webkit-tap-highlight-color: rgba(0,0,0,0);
                -webkit-overflow-scrolling: touch;
                user-select: none;
            }
        }
    }

    // Editor container
    .htMobileEditorContainer {
        display: none;
        position: absolute;
        top: 0;
        width: 70%;
        height: 3.375rem;
        background-color: $gray-200;
        border: $table-border-width solid $table-border-color;
        z-index: 999;
        -webkit-text-size-adjust: none;
        box-sizing: border-box;
        @include border-radius($border-radius-round);

        // Active
        &.active {
            display: block;
        }

        // Inputs
        .inputs {
            position: absolute;
            right: 13.13rem;
            bottom: 0.625rem;
            top: 0.625rem;
            left: 0.875rem;
            height: $input-height;

            textarea {
                font-size: $font-size-base;
                border: $input-border-width solid $input-border-color;
                -webkit-appearance: none;
                position: absolute;
                left: 0.875rem;
                right: 0.875rem;
                top: 0;
                bottom: 0;
                padding: $input-padding-y;
                box-shadow: none;
            }
        }

        // Cell pointer
        .cellPointer {
            position: absolute;
            top: -0.8125rem;
            height: 0;
            width: 0;
            left: 1.875rem;
            border-left: 0.8125rem solid transparent;
            border-right: 0.8125rem solid transparent;
            border-bottom: 0.8125rem solid $gray-300;

            // Hidden
            &.hidden {
                display: none;
            }

            // Arrow
            &:before {
                content: '';
                display: block;
                position: absolute;
                top: 0.125rem;
                height: 0;
                width: 0;
                left: -0.8125rem;
                border-left: 0.8125rem solid transparent;
                border-right: 0.8125rem solid transparent;
                border-bottom: 0.8125rem solid $gray-200;
            }
        }

        // Move handle
        .moveHandle {
            position: absolute;
            top: 0.625rem;
            left: 0.3125rem;
            width: 1.875rem;
            bottom: 0px;
            cursor: move;
            z-index: 9999;

            &:after {
                content: "..\a..\a..\a..";
                white-space: pre;
                line-height: 0.625rem;
                font-size: 1.25rem;
                display: inline-block;
                margin-top: -0.5rem;
                color: $gray-300;
            }
        }

        // Position controls
        .positionControls {
            width: 12.81rem;
            position: absolute;
            right: 0.3125rem;
            top: 0;
            bottom: 0;

            > div {
                width: 3.125rem;
                height: 100%;
                float: left;

                &:after {
                    content: " ";
                    display: block;
                    width: 1rem;
                    height: 1rem;
                    text-align: center;
                    line-height: 3.125rem;
                }
            }
        }


        //
        // Buttons
        //

        // Base
        .leftButton:after,
        .rightButton:after,
        .upButton:after,
        .downButton:after {
            margin: 1.313rem 0 0 1.313rem;
            transform-origin: 0.3125rem;
        }

        // Left button
        .leftButton {
            &:after {
                border-top: 0.125rem solid $hot-cell-highlight-border-color;
                border-left: 0.125rem solid $hot-cell-highlight-border-color;
                transform: rotate(-45deg);
            }

            &:active:after {
                border-color: $gray-400;
            }
        }

        // Right button
        .rightButton {
            &:after {
                border-top: 0.125rem solid $hot-cell-highlight-border-color;
                border-left: 0.125rem solid $hot-cell-highlight-border-color;
                transform: rotate(135deg);
            }

            &:active:after {
                border-color: $gray-400;
            }
        }

        // Up button
        .upButton {
            &:after {
                border-top: 0.125rem solid $hot-cell-highlight-border-color;
                border-left: 0.125rem solid $hot-cell-highlight-border-color;
                transform: rotate(45deg);
            }

            &:active:after {
                border-color: $gray-400;            
            }
        }

        // Down button
        .downButton {
            &:after {
                border-top: 0.125rem solid $hot-cell-highlight-border-color;
                border-left: 0.125rem solid $hot-cell-highlight-border-color;
                transform: rotate(225deg);
            }

            &:active:after {
                border-color: $gray-400;
            }
        }
    }

    // Set z-index
    .topLeftSelectionHandle:not(.ht_master .topLeftSelectionHandle),
    .topLeftSelectionHandle-HitArea:not(.ht_master .topLeftSelectionHandle-HitArea) {
        z-index: 9999;
    }

    // Initial left/top coordinates - overwritten when actual position is set
    .topLeftSelectionHandle,
    .topLeftSelectionHandle-HitArea,
    .bottomRightSelectionHandle,
    .bottomRightSelectionHandle-HitArea {
        left: -10000px;
        top: -10000px;
    }


    //
    // Comments
    //

    // Cell styles
    .htCommentCell {
        position: relative;

        // Corner arrow
        &:after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            border-left: 0.375rem solid transparent;
            border-top: 0.375rem solid red;
        }
    }

    // Comments container
    .htComments {
        display: none;
        z-index: $zindex-tooltip;
        position: absolute;
    }

    // Comment styles
    .htCommentTextArea {
        background-color: $input-bg;
        border: $dropdown-border-width solid $dropdown-border-color;
        padding: $input-padding-y $input-padding-x;
        box-sizing: border-box;
        -webkit-appearance: none;
        @include box-shadow($dropdown-box-shadow);
        @include border-bottom-radius($input-border-radius);

        &:focus {
            outline: 0;
        }
    }


    //
    // Context menu
    //

    .htContextMenu {
        display: none;
        position: absolute;
        z-index: ($zindex-modal + 1);

        // Hide inner tables
        .ht_clone_top,
        .ht_clone_left,
        .ht_clone_corner,
        .ht_clone_debug {
            display: none;
        }

        // Table with content
        table.htCore {
            border: $dropdown-border-width solid $dropdown-border-color;
            background-color: $dropdown-bg;
            padding: $dropdown-padding-y 0;
            @include border-radius($dropdown-border-radius);
            @include box-shadow($dropdown-box-shadow);
        }

        // Hide borders
        .wtBorder {
            visibility: hidden;
        }

        // Table
        table tbody tr {

            // Cells
            td {
                position: relative;
                border-width: 0;
                padding: ($dropdown-item-padding-y / 2) $dropdown-item-padding-x;
                cursor: pointer;
                @include text-truncate();
                @include transition(all ease-in-out $component-transition-timer);

                // No border in first cell
                &:first-child {
                    border: 0;
                }

                // Read only
                &.htDimmed {
                    color: $dropdown-link-color;
                }

                // Hover and active states
                &.current,
                &.zeroclipboard-is-hover {
                    background-color: $dropdown-link-hover-bg;
                }

                // Separator
                &.htSeparator {
                    height: 0;
                    padding: 0;
                    background-color: transparent;
                    cursor: default;

                    > div {
                        height: 1px;
                        background-color: $dropdown-border-color;
                        margin: $dropdown-padding-y 0;
                    }
                }

                // Disabled state
                &.htDisabled {
                    color: $dropdown-link-disabled-color;

                    &,
                    &:hover {
                        cursor: default;
                    }
                }

                // Selected cell
                div span.selected {
                    position: absolute;
                    right: $dropdown-item-padding-x;
                    top: 50%;
                    margin-top: -($icon-font-size / 2);
                    font-size: 0;
                    color: $color-grey-500;

                    // Icon indicator
                    &:after {
                        content: $icon-checkbox-tick;
                        font-family: $icon-font-family;
                        display: inline-block;
                        font-size: $icon-font-size;
                        line-height: 1;
                        @include ll-font-smoothing();
                    }
                }
            }

            // Hidden row
            &.htHidden {
                display: none;
            }
        }

        // Hide overflows in table holder
        .ht_master .wtHolder {
            overflow: hidden;
        }
    }

    // Sub menu
    .htContextMenuSub_Alignment {
        margin-top: -($dropdown-padding-y);
        margin-left: -($dropdown-border-width);
    }

    // Display as buttons
    .global-zeroclipboard-container,
    .global-zeroclipboard-flash-bridge {
        cursor: pointer;
    }

    // Copy - paste textarea
    textarea#HandsontableCopyPaste {
        position: fixed !important;
        top: 0 !important;
        right: 100% !important;
        overflow: hidden;
        opacity: 0;
        outline: 0 none !important;
    }


    //
    // Columns and rows dragging
    //

    // Cursor
    .ht__manualColumnMove.after-selection--columns thead th.ht__highlight,
    .ht__manualColumnMove.on-moving--columns,
    .ht__manualColumnMove.on-moving--columns thead th.ht__highlight,
    .ht__manualRowMove.after-selection--rows tbody th.ht__highlight,
    .ht__manualRowMove.on-moving--rows,
    .ht__manualRowMove.on-moving--rows tbody th.ht__highlight {
        cursor: move;
    }

    // Hide resizers by default
    .ht__manualColumnMove.on-moving--columns .manualColumnResizer,
    .ht__manualRowMove.on-moving--rows .manualRowResizer {
        display: none;
    }

    // Guidline and overlay
    .ht__manualColumnMove--guideline,
    .ht__manualColumnMove--backlight,
    .ht__manualRowMove--guideline,
    .ht__manualRowMove--backlight {
        position: absolute;
        height: 100%;
        display: none;
    }

    // Guidline
    .ht__manualColumnMove--guideline,
    .ht__manualRowMove--guideline {
        background-color: $table-active-bg;
        z-index: 105;
    }
    .ht__manualColumnMove--guideline {
        width: ($table-border-width * 2);
        margin-left: -($table-border-width);
        top: 0;    
    }
    .ht__manualRowMove--guideline {
        height: ($table-border-width * 2);
        margin-top: -($table-border-width);
        left: 0;
    }

    // Overlay
    .ht__manualColumnMove--backlight,
    .ht__manualRowMove--backlight {
        background-color: $table-active-bg;
        display: none;
        z-index: 105;
        pointer-events: none;
    }

    // Show overlay and guideline
    .on-moving--columns.show-ui .ht__manualColumnMove--guideline,
    .on-moving--columns .ht__manualColumnMove--backlight,
    .on-moving--rows.show-ui .ht__manualRowMove--guideline,
    .on-moving--rows .ht__manualRowMove--backlight {
        display: block;
    }



    // Pikaday datepicker
    // ------------------------------

    //
    // Base
    //

    // Single picker
    .pika-single {
        z-index: $zindex-dropdown;
        display: block;
        position: relative;
        background-color: $dropdown-bg;
        border: $dropdown-border-width solid $dropdown-border-color;
        margin-top: -($dropdown-border-width);
        margin-left: -($dropdown-border-width);
        background-clip: padding-box;
        @include border-radius($border-radius);
        @include box-shadow($dropdown-box-shadow);
        @include clearfix;

        // Hide picker
        &.is-hidden {
            display: none;
        }

        // Bounded
        &.is-bound {
            position: absolute;
        }
    }

    // Container
    .pika-lendar {
        float: left;
        max-width: $hot-datepicker-width;
        margin: $datepicker-padding;
    }


    //
    // Header
    //

    // Title
    .pika-title {
        position: relative;
        text-align: center;

        // Select
        select {
            cursor: pointer;
            position: absolute;
            z-index: ($zindex-dropdown - 1);
            margin: 0;
            left: 0;
            height: $input-height;
            padding: $input-padding-y $input-padding-x;
            border: $input-border-width solid $input-border-color;
            opacity: 0;
        }
    }

    // Title labels
    .pika-label {
        display: inline-block;
        position: relative;
        z-index: $zindex-dropdown;
        overflow: hidden;
        margin: 0;
        padding-top: ($datepicker-padding / 2);
        padding-bottom: ($datepicker-padding / 2);
        font-size: $datepicker-title-font-size;
        line-height: 1;

        // Style year select
        & + & {
            margin-left: ($datepicker-padding / 2);
            top: (($font-size-base - $datepicker-title-font-size) / 2);
            font-size: $font-size-base;
            color: $text-muted;
        }
    }


    //
    // Prev and Next navigation
    //

    // Base
    .pika-prev,
    .pika-next {
        display: block;
        position: relative;
        border: 0;
        padding: $datepicker-item-padding;
        font-size: 0;
        background-color: transparent;
        cursor: pointer;
        @include border-radius($border-radius);
        @include transition(all ease-in-out $component-transition-timer);

        // Icons base
        &:after {
            font-family: $icon-font-family;
            display: block;
            font-size: $icon-font-size;
            width: $icon-font-size;
            text-align: center;
            line-height: 1;
            @include ll-font-smoothing();
        }

        // Highlight on hover
        @include hover {
            color: $datepicker-item-hover-color;
            background-color: $datepicker-item-hover-bg;
        }

        // Disabled
        &.is-disabled {
            cursor: default;
            opacity: .2;
        }
    }

    // Previous button
    .pika-prev {
        float: left;

        // Left arrow
        &:after {
            content: $icon-nav-prev;
        }
    }

    // Next button
    .pika-next {
        float: right;

        // Right arrow
        &:after {
            content: $icon-nav-next;
        }
    }


    //
    // Table
    //

    // Base
    .pika-table {
        width: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: 0;
        table-layout: fixed;

        // Set relative position to use angle arrow
        th,
        td {
            position: relative;
        }

        // Day names
        th {
            text-align: center;
            font-weight: $font-weight-normal;
            padding-top: $datepicker-padding;
            padding-bottom: $datepicker-item-padding;
            font-size: $font-size-sm;
            color: $datepicker-item-weekday-color;
        }

        // Day names label
        abbr {
            border-bottom: 0;
            cursor: default;
            text-decoration: none;
        }
    }

    // Buttons inside table
    .pika-button {
        display: block;
        border: 0;
        margin: 0;
        width: 100%;
        padding: $datepicker-item-padding;
        text-align: center;
        background-color: transparent;
        cursor: pointer;
        @include border-radius($border-radius);
        @include transition(all ease-in-out $component-transition-timer);

        // Hover state
        @include hover {
            color: $datepicker-item-hover-color;
            background-color: $datepicker-item-hover-bg;
        }

        // Selected and today highlights
        .is-today & {
            color: $datepicker-item-today-color;
            background-color: $datepicker-item-today-bg;
        }
        .is-selected & {
            color: $datepicker-item-active-color;
            background-color: $datepicker-item-active-bg;
        }
        .is-today &:before,
        .is-selected &:before,
        .is-today.is-selected &:before {
            content: "";
            position: absolute;
            top: ($datepicker-item-padding / 3);
            right: ($datepicker-item-padding / 3);
            border-top: $datepicker-item-padding solid $datepicker-item-active-color;
            border-left: $datepicker-item-padding solid transparent;
            @include size(0);
        }

        // Range
        .is-startrange &,
        .is-endrange & {
            color: $datepicker-item-active-color;
            background-color: $datepicker-item-active-bg;
        }
        .is-inrange & {
            color: $datepicker-item-hover-color;
            background-color: $datepicker-item-hover-bg;
        }

        // Disabled button
        .is-disabled & {
            pointer-events: none;
            cursor: $cursor-disabled;
            opacity: 0.5;
        }
    }


    //
    // Others
    //

    // Select
    .pika-select {
        display: inline-block;
    }

    // Week select
    .pika-week {
        font-size: $font-size-sm;
        color: $datepicker-item-weekday-color;
    }

    /*rtl:end:ignore*/
}
