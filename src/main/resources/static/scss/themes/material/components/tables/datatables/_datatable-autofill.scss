/* ------------------------------------------------------------------------------
 *
 *  # Autofill extension
 *
 *  Spreadsheets such as Excel and Google Docs have a very handy data duplication
 *  option of an auto fill tool
 *
 * ---------------------------------------------------------------------------- */

// Check if component is enabled
@if $enable-datatable-autofill {

    // Handle
    .dt-autofill-handle {
        position: absolute;
        z-index: 102;
        border: $table-border-width solid theme-color("primary");
        background-color: theme-color("primary");
        @include size(0.5rem);
    }

    // Select frame
    .dt-autofill-select {
        position: absolute;
        z-index: 1001;
        background-color: theme-color("primary");
        background-image: repeating-linear-gradient(45deg, transparent, transparent 0.3125rem, rgba($white, 0.75) 0.3125rem, rgba($white, 0.75) 0.625rem);

        // Vertical lines
        &.top,
        &.bottom {
            height: $table-border-width;
        }

        // Horizontal lines
        &.left,
        &.right {
            width: $table-border-width;
        }
    }

    // Confirmation dialog
    .dt-autofill-list {
        position: fixed;
        top: 50%;
        left: 50%;
        width: 31.25rem;
        margin-left: -15.63rem;
        background-color: $modal-content-bg;
        border: $modal-content-border-width solid $modal-content-border-color;
        z-index: $zindex-modal;
        padding: $dropdown-padding-y 0;
        @include border-radius($border-radius);

        // List
        ul {
            display: table;
            margin: 0;
            padding: 0;
            list-style: none;
            width: 100%;

            // List items
            li {
                display: table-row;
                @include transition(all ease-in-out $component-transition-timer);

                // Highlight row on hover
                @include hover {
                    color: $dropdown-link-hover-color;
                    background-color: $dropdown-link-hover-bg;
                }
            }
        }

        // Text label
        .dt-autofill-question {
            display: table-cell;
            padding: $dropdown-item-padding-y $dropdown-item-padding-x;

            // Text label input
            input[type=number] {
                padding: 0.375rem;
                width: 1.875rem;
                margin: -0.125rem 0;
            }
        }

        // Confirm button
        .dt-autofill-button {
            display: table-cell;
            padding: $dropdown-item-padding-y $dropdown-item-padding-x;
            text-align: right;

            // Button itself
            .btn {
                padding: 0.1875rem;
                background-color: theme-color("primary");
                color: $white;
                font-size: 0;

                // Checkmark icon
                &:after {
                    content: $icon-table-button-confirm;
                    font-family: $icon-font-family;
                    display: block;
                    font-size: $icon-font-size;
                    width: $icon-font-size;
                    line-height: 1;
                    @include ll-font-smoothing();
                }

                // Mute button on hover
                @include hover {
                    opacity: 0.85;
                }
            }
        }
    }

    // Overlay
    .dt-autofill-background {
        position: fixed;
        top: 0;
        left: 0;
        background-color: $modal-backdrop-bg;
        z-index: $zindex-modal-backdrop;
        opacity: $modal-backdrop-opacity;
        @include size(100%);
    }
}
