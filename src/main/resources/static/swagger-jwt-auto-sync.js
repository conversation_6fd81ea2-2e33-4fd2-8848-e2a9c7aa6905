/**
 * Simple JWT Cookie Sync for Swagger UI
 */
(function() {
    'use strict';

    let lastToken = null;

    function getJwtFromSwagger() {
        if (window.ui && window.ui.authSelectors) {
            try {
                const auth = window.ui.authSelectors.authorized();
                const bearerAuth = auth && auth.get && auth.get('bearerAuth');
                const token = bearerAuth && bearerAuth.get && bearerAuth.get('value');
                return token && token.startsWith('eyJ') ? token : null;
            } catch (e) {
                return null;
            }
        }
        return null;
    }

    function setJwtCookie(token) {
        document.cookie = `jwt=${token}; path=/VMS; max-age=7200; SameSite=Lax`;
    }

    function clearJwtCookie() {
        document.cookie = 'jwt=; path=/VMS; max-age=0';
    }

    function syncJwt() {
        const currentToken = getJwtFromSwagger();
        if (currentToken !== lastToken) {
            lastToken = currentToken;
            if (currentToken) {
                setJwtCookie(currentToken);
            } else {
                clearJwtCookie();
            }
        }
    }

    // Wait for Swagger UI to load, then start syncing
    function init() {
        if (window.ui) {
            // Listen for auth button clicks
            document.addEventListener('click', function(e) {
                if (e.target.textContent && e.target.textContent.toLowerCase().includes('authorize')) {
                    setTimeout(syncJwt, 1000);
                }
            });

            // Periodic sync
            setInterval(syncJwt, 3000);
            syncJwt();
        } else {
            setTimeout(init, 500);
        }
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
})();
