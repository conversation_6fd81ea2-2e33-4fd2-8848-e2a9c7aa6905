/**
 * Swagger JWT Auto-Sync Script
 * Automatically synchronizes JWT tokens between Swagger UI Authorization and browser cookies
 */

(function() {
    'use strict';

    // Configuration
    const JWT_COOKIE_NAME = 'jwt';
    const COOKIE_PATH = '/VMS';
    const COOKIE_MAX_AGE = 7200; // 2 hours
    const CHECK_INTERVAL = 2000; // Check every 2 seconds
    let lastAuthToken = null;
    let syncEnabled = true;

    console.log('🚀 JWT Auto-Sync script loaded');

    /**
     * Validate JWT token format
     */
    function isValidJWT(token) {
        return token && 
               typeof token === 'string' && 
               token.length > 50 && 
               token.includes('.') && 
               token.startsWith('eyJ');
    }

    /**
     * Get JWT token from Swagger UI with multiple detection methods
     */
    function getSwaggerAuthToken() {
        // Method 1: Check Swagger UI's internal state (most reliable)
        if (window.ui && window.ui.authSelectors) {
            try {
                const auth = window.ui.authSelectors.authorized();
                if (auth && auth.get) {
                    const bearerAuth = auth.get('bearerAuth');
                    if (bearerAuth && bearerAuth.get) {
                        const token = bearerAuth.get('value');
                        if (isValidJWT(token)) {
                            console.debug('JWT found via Swagger UI internal state');
                            return token;
                        }
                    }
                }
            } catch (e) {
                console.debug('Swagger auth state check failed:', e);
            }
        }

        // Method 2: Check localStorage for persisted auth
        try {
            const swaggerAuth = localStorage.getItem('swagger-ui-auth');
            if (swaggerAuth) {
                const authData = JSON.parse(swaggerAuth);
                if (authData.bearerAuth && authData.bearerAuth.value) {
                    const token = authData.bearerAuth.value;
                    if (isValidJWT(token)) {
                        console.debug('JWT found via localStorage');
                        return token;
                    }
                }
            }
        } catch (e) {
            console.debug('localStorage check failed:', e);
        }

        // Method 3: Look for authorization input fields (fallback)
        const authInputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        for (let input of authInputs) {
            const value = input.value;
            if (isValidJWT(value)) {
                console.debug('JWT found via input field');
                return value;
            }
        }

        return null;
    }

    /**
     * Get current JWT from cookie
     */
    function getCurrentJwtCookie() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === JWT_COOKIE_NAME) {
                return value;
            }
        }
        return null;
    }

    /**
     * Set JWT cookie with proper security settings
     */
    function setJwtCookie(token) {
        if (!token) return;
        
        const cookieValue = `${JWT_COOKIE_NAME}=${token}; path=${COOKIE_PATH}; max-age=${COOKIE_MAX_AGE}; SameSite=Lax`;
        document.cookie = cookieValue;
        console.log('🍪 JWT cookie set successfully');
    }

    /**
     * Clear JWT cookie
     */
    function clearJwtCookie() {
        document.cookie = `${JWT_COOKIE_NAME}=; path=${COOKIE_PATH}; max-age=0`;
        console.log('🗑️ JWT cookie cleared');
    }

    /**
     * Show notification to user
     */
    function showNotification(message, type = 'info') {
        console.log(`[JWT Auto-Sync] ${message}`);
        
        // Create notification element
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
            max-width: 300px;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    /**
     * Main synchronization function
     */
    function syncJwtToken() {
        if (!syncEnabled) return;

        try {
            const currentAuthToken = getSwaggerAuthToken();
            const currentCookie = getCurrentJwtCookie();
            
            // Check if auth token has changed
            if (currentAuthToken !== lastAuthToken) {
                lastAuthToken = currentAuthToken;
                
                if (currentAuthToken) {
                    // Update cookie with new token
                    if (currentAuthToken !== currentCookie) {
                        setJwtCookie(currentAuthToken);
                        showNotification('🔄 JWT token synchronized to cookie', 'success');
                    }
                } else {
                    // Clear cookie if no auth token and cookie exists
                    if (currentCookie) {
                        clearJwtCookie();
                        showNotification('🗑️ JWT cookie cleared', 'info');
                    }
                }
            }
        } catch (e) {
            console.debug('JWT sync error:', e);
        }
    }

    /**
     * Add event listeners for Swagger UI interactions
     */
    function addSwaggerUIListeners() {
        // Listen for clicks on authorize/logout buttons
        document.addEventListener('click', function(e) {
            const target = e.target;
            if (target && target.textContent) {
                const text = target.textContent.toLowerCase();
                if (text.includes('authorize') || 
                    text.includes('logout') ||
                    target.classList.contains('btn-done') ||
                    target.classList.contains('authorize')) {
                    
                    console.debug('Auth button clicked, scheduling sync');
                    setTimeout(syncJwtToken, 500);
                    setTimeout(syncJwtToken, 2000);
                }
            }
        });

        // Listen for input changes in auth fields
        document.addEventListener('input', function(e) {
            const target = e.target;
            if (target && target.type === 'text' && target.value && target.value.startsWith('eyJ')) {
                console.debug('JWT input detected, scheduling sync');
                setTimeout(syncJwtToken, 1000);
            }
        });
    }

    /**
     * Enhanced DOM monitoring
     */
    function setupDOMObserver() {
        const observer = new MutationObserver(function(mutations) {
            let shouldCheck = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' || 
                    (mutation.type === 'attributes' && 
                     (mutation.attributeName === 'value' || 
                      mutation.attributeName === 'class'))) {
                    shouldCheck = true;
                }
            });
            if (shouldCheck) {
                setTimeout(syncJwtToken, 300);
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['value', 'class']
        });
        
        console.debug('DOM observer setup complete');
    }

    /**
     * Initialize JWT synchronization
     */
    function initJwtSync() {
        console.log('🔄 Initializing JWT auto-sync');

        // Add event listeners
        addSwaggerUIListeners();

        // Setup DOM observer
        setupDOMObserver();

        // Start periodic checking
        setInterval(syncJwtToken, CHECK_INTERVAL);

        // Initial sync
        setTimeout(syncJwtToken, 1000);

        console.log('✅ JWT auto-sync initialized successfully');
    }

    /**
     * Wait for Swagger UI to load
     */
    function waitForSwaggerUI() {
        if (document.querySelector('.swagger-ui') || window.ui) {
            initJwtSync();
        } else {
            setTimeout(waitForSwaggerUI, 500);
        }
    }

    // Start when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', waitForSwaggerUI);
    } else {
        waitForSwaggerUI();
    }

    // Expose API for manual control
    window.jwtAutoSync = {
        sync: syncJwtToken,
        enable: () => { syncEnabled = true; console.log('JWT auto-sync enabled'); },
        disable: () => { syncEnabled = false; console.log('JWT auto-sync disabled'); },
        getCurrentToken: getSwaggerAuthToken,
        getCurrentCookie: getCurrentJwtCookie,
        setJwtCookie: setJwtCookie,
        clearJwtCookie: clearJwtCookie,
        status: () => ({ 
            enabled: syncEnabled, 
            lastToken: lastAuthToken,
            currentToken: getSwaggerAuthToken(),
            currentCookie: getCurrentJwtCookie()
        })
    };

})();
