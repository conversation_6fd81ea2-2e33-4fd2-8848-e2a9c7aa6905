package com.eduhk.sa.config.swagger;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
@ConditionalOnProperty(name = "swagger.enabled", havingValue = "true")
public class SwaggerJwtCookieFilter extends OncePerRequestFilter {

    @Value("${swagger.jwt-cookie-name:jwt}")
    private String jwtCookieName;

    @Value("${swagger.jwt:jwt}")
    private String jwt;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        // Disabled automatic JWT cookie setting to prevent overriding user-set tokens
        // Users can now fully control JWT tokens through:
        // 1. JWT Management page
        // 2. Swagger UI Authorize button (with auto-sync)
        // 3. Manual API calls with Authorization header

        // Simply pass through without setting any default JWT cookie
        filterChain.doFilter(request, response);
    }
}