package com.eduhk.sa.config.jwt;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;

@Slf4j
@RequiredArgsConstructor
public class JWTAuthenticationFilter extends OncePerRequestFilter {

    private final JWTTokenProvider tokenProvider;
    private final JWTCookieUtil cookieUtil;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, Fi<PERSON><PERSON>hain filterChain)
            throws ServletException, IOException {

        try {
            String jwt = resolveToken(request);

            // Auto-sync JWT from Authorization header to cookie for Swagger UI
            autoSyncJwtToCookie(request, response, jwt);

            if (StringUtils.hasText(jwt) && tokenProvider.validateToken(jwt)) {
                Authentication authentication = tokenProvider.getAuthentication(jwt);
                SecurityContextHolder.getContext().setAuthentication(authentication);
                log.debug("Set Authentication to security context for '{}', uri: {}",
                        authentication.getName(), request.getRequestURI());
            }
        } catch (Exception e) {
            log.error("Cannot set user authentication: {}", e.getMessage());
        }

        filterChain.doFilter(request, response);
    }

    private String resolveToken(HttpServletRequest request) {
        // First try to get token from Authorization header (for Swagger UI)
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            String token = bearerToken.substring(7);
            log.debug("JWT token found in Authorization header");
            return token;
        }

        // Fallback to cookie (for JWT management page)
        Optional<String> tokenFromCookie = cookieUtil.getAccessTokenFromCookies(request);
        if (tokenFromCookie.isPresent()) {
            log.debug("JWT token found in cookie");
            return tokenFromCookie.get();
        }

        return null;
    }

    /**
     * Auto-sync JWT from Authorization header to cookie for Swagger UI
     */
    private void autoSyncJwtToCookie(HttpServletRequest request, HttpServletResponse response, String jwt) {
        try {
            // Check if JWT came from Authorization header (indicating Swagger UI usage)
            String bearerToken = request.getHeader("Authorization");
            if (!StringUtils.hasText(bearerToken) || !bearerToken.startsWith("Bearer ")) {
                return; // No Authorization header, skip sync
            }

            String headerToken = bearerToken.substring(7);
            if (!StringUtils.hasText(headerToken)) {
                return; // Empty token, skip sync
            }

            // Check if current cookie has different token
            Optional<String> currentCookie = cookieUtil.getAccessTokenFromCookies(request);
            if (!currentCookie.isPresent() || !currentCookie.get().equals(headerToken)) {
                // Set JWT cookie with proper path
                Cookie jwtCookie = new Cookie("jwt", headerToken);
                jwtCookie.setPath("/VMS");
                jwtCookie.setMaxAge(7200); // 2 hours
                jwtCookie.setHttpOnly(false); // Allow JavaScript access
                jwtCookie.setSecure(request.isSecure());
                response.addCookie(jwtCookie);

                log.info("JWT token auto-synced from Authorization header to cookie for URI: {}", request.getRequestURI());
            }
        } catch (Exception e) {
            log.warn("Could not auto-sync JWT to cookie: {}", e.getMessage());
        }
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) {
        String uri = request.getRequestURI();
        // Apply filter to admission APIs and Swagger UI resources for JWT sync
        return !(uri.startsWith("/VMS/admission") ||
                uri.contains("/swagger-ui") ||
                uri.contains("/v3/api-docs"));
    }
}
