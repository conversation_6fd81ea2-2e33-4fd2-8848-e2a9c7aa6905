# JWT Cookie 自動同步實現總結

## 🎯 實現目標
✅ **在 Swagger UI 中設置 JWT token 時自動同步到 Cookie，替代硬編碼方式**

## 📁 修改的文件

### **新創建的文件**
1. `src/main/resources/static/swagger-jwt-auto-sync.js` - JWT 自動同步腳本

### **修改的配置文件**
1. `src/main/resources/application-local.properties` - Cookie 路徑: `/VMS`
2. `src/main/resources/application-dev.properties` - Cookie 路徑: `/VMS`
3. `src/main/resources/application-prod.properties` - Cookie 路徑: `/VMS`
4. `src/main/resources/application-uat.properties` - Cookie 路徑: `/VMS`

### **修改的 Java 文件**
1. `src/main/java/com/eduhk/sa/config/jwt/JWTAuthenticationFilter.java`
   - Cookie 路徑統一為 `/VMS`
   - 保留服務端自動同步邏輯

2. `src/main/java/com/eduhk/sa/config/swagger/SwaggerJwtCookieFilter.java`
   - 禁用硬編碼 JWT 設置

### **現有文件**
- `src/main/resources/static/swagger-ui/index.html` - 已引用自動同步腳本

## 🚀 工作原理

### **自動同步機制**
1. **多重檢測**: 
   - Swagger UI 內部狀態（最可靠）
   - localStorage 檢查（備用）
   - DOM 輸入框檢查（最後手段）

2. **智能同步**: 
   - 只在 token 變化時同步
   - 自動清除過期 token
   - 防止重複設置

3. **雙重保障**:
   - 客戶端 JavaScript 同步
   - 服務端 Filter 同步

## 🔧 使用方法

### **在 Swagger UI 中使用**
1. 訪問 `http://localhost:8080/VMS/swagger-ui/index.html`
2. 點擊 "Authorize" 按鈕
3. 輸入 JWT token
4. Token 自動同步到 Cookie
5. 可以正常調用需要認證的 API

## ⚙️ 技術細節

### **Cookie 配置**
- **名稱**: `jwt`
- **路徑**: `/VMS`
- **過期時間**: 2 小時 (7200 秒)
- **安全屬性**: `SameSite=Lax`

### **同步頻率**
- DOM 變化時立即檢查
- 按鈕點擊後延遲檢查 (500ms, 2000ms)
- 定期檢查 (每 2 秒)

## ✅ 驗證方法

### **基本測試**
1. 啟動應用: `mvn spring-boot:run -Dspring-boot.run.profiles=local`
2. 訪問 Swagger UI: `http://localhost:8080/VMS/swagger-ui/index.html`
3. 在 Authorize 中輸入 JWT token
4. 檢查瀏覽器控制台日誌: `[JWT Auto-Sync] JWT token synchronized to cookie`

### **Cookie 驗證**
在瀏覽器控制台執行:
```javascript
document.cookie.split(';').forEach(c => console.log(c.trim()));
```

### **API 測試**
設置 JWT 後，直接調用需要認證的 API 端點，應該能正常工作。

## 🎉 最終效果

1. **無縫體驗**: 在 Swagger UI 設置 JWT 後立即可用於 API 調用
2. **動態管理**: 完全替代硬編碼 JWT
3. **自動化**: 無需手動操作，自動檢測和同步
4. **可靠性**: 多重檢測機制確保同步成功
5. **安全性**: 正確的 Cookie 配置和路徑設置

## 🔧 故障排除

### **如果同步不工作**
1. 檢查瀏覽器控制台是否有錯誤
2. 確認 Swagger UI 完全載入
3. 檢查 JWT token 格式是否正確（以 `eyJ` 開頭）
4. 驗證 Cookie 路徑配置是否為 `/VMS`

### **手動控制**
如需手動控制，可在瀏覽器控制台使用:
```javascript
// 手動同步
window.jwtAutoSync.sync()

// 啟用/禁用自動同步
window.jwtAutoSync.enable()
window.jwtAutoSync.disable()
```

## 📝 注意事項

1. 所有環境的 Cookie 路徑已統一為 `/VMS`
2. 硬編碼 JWT 已完全禁用
3. 自動同步僅在 token 變化時觸發
4. 包含完整的錯誤處理和日誌記錄
5. 適用於所有支持的角色: ADMIN, SUPER_ADMIN, APPLICANT
