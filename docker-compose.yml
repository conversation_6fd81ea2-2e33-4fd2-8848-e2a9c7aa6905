version: "3.8"
services:
  mailhog:
    image: mailhog/mailhog
    container_name: mailhog_vms
    ports:
      - "1025:1025"
      - "8025:8025"
  
  oracle:
    image: container-registry.oracle.com/database/express:latest
    container_name: oracle_vms
    environment:
      - ORACLE_PWD=vms2025local
      - ORACLE_CHARACTERSET=AL32UTF8
    ports:
      - "1521:1521"
      - "5500:5500"
    volumes:
      - oracle_data:/opt/oracle/oradata
      - ./init-scripts:/opt/oracle/scripts/startup
    healthcheck:
      test: ["CMD", "sqlplus", "-L", "sys/vms2025local@//localhost:1521/XEPDB1 as sysdba", "@healthcheck.sql"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  oracle_data: