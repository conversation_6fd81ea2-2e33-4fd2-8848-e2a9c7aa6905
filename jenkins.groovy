pipeline {
    agent any
    environment {
        AS_CREDS = credentials("jboss7-${host}")
        GITLAB_USERNAME = "jenkins"
        GITLAB_PAT = "**************************"
        ENVIRONMENT = ''
        BRANCH = ''
        NODE_VERSION = "22.14.0"
        NODE_HOME = ''
    }
    tools {
        jdk 'Oracle JDK 8'
    }
    stages {
        stage('Check & Install Node.js') {
            steps {
                script {
                    def nodeExists = bat(script: 'where node', returnStatus: true)

                    if (nodeExists == 0) {
                        echo "✅ Node.js is already installed."
                    } else {
                        echo "⚠️ Node.js not found. Installing Node.js ${NODE_VERSION}..."

                        bat """
                        curl -o nodejs.msi https://nodejs.org/dist/v${NODE_VERSION}/node-v${NODE_VERSION}-x64.msi
                        msiexec /i nodejs.msi /quiet /norestart
                        del nodejs.msi
                        """
                    }

                    // Manually set the PATH variable
                    env.PATH = "C:\\Program Files\\nodejs;${env.PATH}"

                    def newNodeVersion = bat(script: 'node -v', returnStdout: true).trim()
                    echo "✅ Node.js version: ${newNodeVersion}"
                }
            }
        }

        stage('Determine Environment') {
            steps {
                script {
                    def selectedHost = params.host

                    switch (selectedHost) {
                        case 'dappl04':
                            ENVIRONMENT = 'dev'
                            BRANCH = 'dev'
                            break
                        case 'uappl04':
                            ENVIRONMENT = 'uat'
                            BRANCH = 'uat'
                            break
                        case ['pappl04', 'pappl05']:
                            ENVIRONMENT = 'prod'
                            BRANCH = 'master'
                            break
                        default:
                            error "Unknown environment: ${selectedChoice}"
                    }

                    // Set the environment variable
                    env.ENVIRONMENT = ENVIRONMENT

                    echo "Selected host: ${selectedHost}"
                    echo "Mapped environment: ${ENVIRONMENT}"
                }
            }
        }

        stage('Checkout Backend') {
            steps {
                script {
                    dir('VMS') {
                        git url: "http://${GITLAB_USERNAME}:${GITLAB_PAT}@repo.eduhk.hk:8080/sa_team/VMS.git",
                                branch: "${BRANCH}"
                    }
                }
            }
        }

        stage('Checkout Frontend') {
            steps {
                script {
                    dir('eduhk-vms-web') {
                        git url: "http://${GITLAB_USERNAME}:${GITLAB_PAT}@repo.eduhk.hk:8080/admission/eduhk-vms-web.git",
                                branch: "${BRANCH}"
                    }
                }
            }
        }

        stage('Build Frontend') {
            steps {
                script {
                    dir('eduhk-vms-web') {
                        bat """
                        copy .env.${ENVIRONMENT} .env
                        npm install & npm run build 
                        if %ERRORLEVEL% neq 0 exit /b %ERRORLEVEL%
                        """
                    }
                }
            }
        }

        stage('Move Frontend Build to Backend') {
            steps {
                script {
                    bat """
                    REM Create directories if they don't exist
                    if not exist VMS\\src\\main\\resources\\static\\ (
                        mkdir VMS\\src\\main\\resources\\static\\
                    )
                    if not exist VMS\\src\\main\\resources\\templates\\admission\\ (
                        mkdir VMS\\src\\main\\resources\\templates\\admission\\
                    )
                    
                    REM Copy only specific folders from dist to static folder
                    xcopy /E /I /Y eduhk-vms-web\\dist\\static\\* VMS\\src\\main\\resources\\static\\static\\
                    xcopy /E /I /Y eduhk-vms-web\\dist\\images\\* VMS\\src\\main\\resources\\static\\images\\
                    xcopy /E /I /Y eduhk-vms-web\\dist\\fonts\\* VMS\\src\\main\\resources\\static\\fonts\\
        
                    REM Copy template index.html to templates folder
                     copy eduhk-vms-web\\dist\\index.html VMS\\src\\main\\resources\\templates\\admission\\index.html
                    """
                }
            }
        }

        stage('Update application.properties') {
            steps {
                script {
                    dir('VMS') {
                        def propertiesFile = 'src/main/resources/application.properties'

                        if (fileExists(propertiesFile)) {
                            def props = readFile(propertiesFile)

                            props = props.replaceAll(/\$\{ENVIRONMENT\}/, "${ENVIRONMENT}")

                            writeFile(file: propertiesFile, text: props)

                            echo "Updated application.properties successfully."
                        } else {
                            echo "${propertiesFile} does not exist. Skipping update."
                        }
                    }
                }
            }
        }

        stage('Build & Package Backend') {
            steps {
                script {
                    dir('VMS') {
                        bat(/mvn clean package -DskipTests -Dspring.profiles.active=${ENVIRONMENT}/)
                    }
                }
            }
        }


        stage('Deploy to JBoss') {
            steps {
                script {
                    dir('VMS') {
                        bat(/mvn wildfly:deploy-only -Dapp.server=${host}.eduhk.hk -Dapp.server.username=$AS_CREDS_USR -Dapp.server.password=$AS_CREDS_PSW -Dspring.profiles.active=${ENVIRONMENT}/)
                    }
                }
            }
        }
    }
}